version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: harvest-profit-db
    environment:
      POSTGRES_DB: harvest_profit_pro_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - harvest-profit-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: harvest-profit-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - harvest-profit-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: harvest-profit-backend
    environment:
      NODE_ENV: development
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: harvest_profit_pro_dev
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - harvest-profit-network
    command: npm run dev

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: harvest-profit-frontend
    environment:
      VITE_API_BASE_URL: http://localhost:3001/api
      VITE_WS_URL: http://localhost:3001
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - harvest-profit-network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  harvest-profit-network:
    driver: bridge
