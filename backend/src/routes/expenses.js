const express = require('express');
const { Op } = require('sequelize');
const { Expense, Field } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { validateExpense, validateUUID, validatePagination } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * /api/expenses:
 *   get:
 *     summary: Get user's expenses
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by expense category
 *       - in: query
 *         name: fieldId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by field ID
 *     responses:
 *       200:
 *         description: Expenses retrieved successfully
 */
router.get('/', authenticateToken, validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 20, category, fieldId, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;

    // Build where clause
    const where = { userId: req.user.id };
    if (category) where.category = category;
    if (fieldId) where.fieldId = fieldId;
    if (startDate || endDate) {
      where.date = {};
      if (startDate) where.date[Op.gte] = startDate;
      if (endDate) where.date[Op.lte] = endDate;
    }

    const { count, rows: expenses } = await Expense.findAndCountAll({
      where,
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ],
      order: [['date', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      expenses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get expenses error:', error);
    res.status(500).json({
      error: 'Failed to retrieve expenses'
    });
  }
});

/**
 * @swagger
 * /api/expenses:
 *   post:
 *     summary: Create new expense
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - category
 *               - description
 *               - amount
 *               - date
 *             properties:
 *               category:
 *                 type: string
 *                 enum: [seed, fertilizer, pesticide, fuel, equipment, labor, insurance, utilities, maintenance, transportation, storage, marketing, professional_services, other]
 *               description:
 *                 type: string
 *               amount:
 *                 type: number
 *               date:
 *                 type: string
 *                 format: date
 *               fieldId:
 *                 type: string
 *                 format: uuid
 *               quantity:
 *                 type: number
 *               unit:
 *                 type: string
 *               vendor:
 *                 type: string
 *     responses:
 *       201:
 *         description: Expense created successfully
 */
router.post('/', authenticateToken, validateExpense, async (req, res) => {
  try {
    const expenseData = {
      ...req.body,
      userId: req.user.id
    };

    // Validate field ownership if fieldId is provided
    if (expenseData.fieldId) {
      const field = await Field.findOne({
        where: { id: expenseData.fieldId, userId: req.user.id }
      });
      if (!field) {
        return res.status(400).json({
          error: 'Invalid field ID or field does not belong to user'
        });
      }
    }

    const expense = await Expense.create(expenseData);

    // Include field data in response
    const expenseWithField = await Expense.findByPk(expense.id, {
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ]
    });

    res.status(201).json({
      message: 'Expense created successfully',
      expense: expenseWithField
    });
  } catch (error) {
    console.error('Create expense error:', error);
    res.status(500).json({
      error: 'Failed to create expense'
    });
  }
});

/**
 * @swagger
 * /api/expenses/{id}:
 *   get:
 *     summary: Get expense by ID
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Expense retrieved successfully
 *       404:
 *         description: Expense not found
 */
router.get('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const expense = await Expense.findOne({
      where: { id: req.params.id, userId: req.user.id },
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ]
    });

    if (!expense) {
      return res.status(404).json({
        error: 'Expense not found'
      });
    }

    res.json({ expense });
  } catch (error) {
    console.error('Get expense error:', error);
    res.status(500).json({
      error: 'Failed to retrieve expense'
    });
  }
});

/**
 * @swagger
 * /api/expenses/{id}:
 *   put:
 *     summary: Update expense
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Expense updated successfully
 *       404:
 *         description: Expense not found
 */
router.put('/:id', authenticateToken, validateUUID('id'), validateExpense, async (req, res) => {
  try {
    const expense = await Expense.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!expense) {
      return res.status(404).json({
        error: 'Expense not found'
      });
    }

    // Validate field ownership if fieldId is being updated
    if (req.body.fieldId && req.body.fieldId !== expense.fieldId) {
      const field = await Field.findOne({
        where: { id: req.body.fieldId, userId: req.user.id }
      });
      if (!field) {
        return res.status(400).json({
          error: 'Invalid field ID or field does not belong to user'
        });
      }
    }

    await expense.update(req.body);

    // Include field data in response
    const updatedExpense = await Expense.findByPk(expense.id, {
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ]
    });

    res.json({
      message: 'Expense updated successfully',
      expense: updatedExpense
    });
  } catch (error) {
    console.error('Update expense error:', error);
    res.status(500).json({
      error: 'Failed to update expense'
    });
  }
});

/**
 * @swagger
 * /api/expenses/{id}:
 *   delete:
 *     summary: Delete expense
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Expense deleted successfully
 *       404:
 *         description: Expense not found
 */
router.delete('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const expense = await Expense.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!expense) {
      return res.status(404).json({
        error: 'Expense not found'
      });
    }

    await expense.destroy();

    res.json({
      message: 'Expense deleted successfully'
    });
  } catch (error) {
    console.error('Delete expense error:', error);
    res.status(500).json({
      error: 'Failed to delete expense'
    });
  }
});

module.exports = router;
