const express = require('express');
const { Field, Expense } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { validateField, validateUUID, validatePagination } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * /api/fields:
 *   get:
 *     summary: Get user's fields
 *     tags: [Fields]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Fields retrieved successfully
 */
router.get('/', authenticateToken, validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 20, isActive } = req.query;
    const offset = (page - 1) * limit;

    const where = { userId: req.user.id };
    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }

    const { count, rows: fields } = await Field.findAndCountAll({
      where,
      include: [
        {
          model: Expense,
          as: 'expenses',
          attributes: ['id', 'amount', 'category', 'date']
        }
      ],
      order: [['name', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      fields,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get fields error:', error);
    res.status(500).json({
      error: 'Failed to retrieve fields'
    });
  }
});

/**
 * @swagger
 * /api/fields:
 *   post:
 *     summary: Create new field
 *     tags: [Fields]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - acres
 *             properties:
 *               name:
 *                 type: string
 *               acres:
 *                 type: number
 *               location:
 *                 type: string
 *               soilType:
 *                 type: string
 *               currentCrop:
 *                 type: string
 *     responses:
 *       201:
 *         description: Field created successfully
 */
router.post('/', authenticateToken, validateField, async (req, res) => {
  try {
    const fieldData = {
      ...req.body,
      userId: req.user.id
    };

    const field = await Field.create(fieldData);

    res.status(201).json({
      message: 'Field created successfully',
      field
    });
  } catch (error) {
    console.error('Create field error:', error);
    res.status(500).json({
      error: 'Failed to create field'
    });
  }
});

/**
 * @swagger
 * /api/fields/{id}:
 *   get:
 *     summary: Get field by ID
 *     tags: [Fields]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Field retrieved successfully
 *       404:
 *         description: Field not found
 */
router.get('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const field = await Field.findOne({
      where: { id: req.params.id, userId: req.user.id },
      include: [
        {
          model: Expense,
          as: 'expenses',
          order: [['date', 'DESC']]
        }
      ]
    });

    if (!field) {
      return res.status(404).json({
        error: 'Field not found'
      });
    }

    res.json({ field });
  } catch (error) {
    console.error('Get field error:', error);
    res.status(500).json({
      error: 'Failed to retrieve field'
    });
  }
});

/**
 * @swagger
 * /api/fields/{id}:
 *   put:
 *     summary: Update field
 *     tags: [Fields]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Field updated successfully
 *       404:
 *         description: Field not found
 */
router.put('/:id', authenticateToken, validateUUID('id'), validateField, async (req, res) => {
  try {
    const field = await Field.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!field) {
      return res.status(404).json({
        error: 'Field not found'
      });
    }

    await field.update(req.body);

    res.json({
      message: 'Field updated successfully',
      field
    });
  } catch (error) {
    console.error('Update field error:', error);
    res.status(500).json({
      error: 'Failed to update field'
    });
  }
});

/**
 * @swagger
 * /api/fields/{id}:
 *   delete:
 *     summary: Delete field
 *     tags: [Fields]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Field deleted successfully
 *       404:
 *         description: Field not found
 */
router.delete('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const field = await Field.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!field) {
      return res.status(404).json({
        error: 'Field not found'
      });
    }

    // Soft delete by setting isActive to false
    await field.update({ isActive: false });

    res.json({
      message: 'Field deleted successfully'
    });
  } catch (error) {
    console.error('Delete field error:', error);
    res.status(500).json({
      error: 'Failed to delete field'
    });
  }
});

module.exports = router;
