const express = require('express');
const { Op } = require('sequelize');
const { Equipment, EquipmentUsage } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { validateUUID, validatePagination } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * /api/equipment:
 *   get:
 *     summary: Get user's equipment
 *     tags: [Equipment]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Equipment retrieved successfully
 */
// Specific routes must come before parameterized routes
/**
 * @swagger
 * /api/equipment/maintenance/alerts:
 *   get:
 *     summary: Get maintenance alerts for equipment
 *     tags: [Equipment]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Maintenance alerts retrieved successfully
 */
router.get('/maintenance/alerts', authenticateToken, async (req, res) => {
  try {
    const alerts = await Equipment.findAll({
      where: {
        userId: req.user.id,
        isActive: true,
        nextMaintenanceDate: {
          [Op.lte]: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
        }
      },
      order: [['nextMaintenanceDate', 'ASC']]
    });

    res.json(alerts);
  } catch (error) {
    console.error('Error fetching maintenance alerts:', error);
    res.status(500).json({
      error: 'Failed to fetch maintenance alerts',
      details: error.message
    });
  }
});

router.get('/', authenticateToken, validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 20, type, isActive } = req.query;
    const offset = (page - 1) * limit;

    const where = { userId: req.user.id };
    if (type) where.type = type;
    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }

    const { count, rows: equipment } = await Equipment.findAndCountAll({
      where,
      include: [
        {
          model: EquipmentUsage,
          as: 'usageRecords',
          attributes: ['id', 'date', 'hoursUsed', 'fuelCost'],
          limit: 5,
          order: [['date', 'DESC']]
        }
      ],
      order: [['name', 'ASC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      equipment,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get equipment error:', error);
    res.status(500).json({
      error: 'Failed to retrieve equipment'
    });
  }
});

/**
 * @swagger
 * /api/equipment:
 *   post:
 *     summary: Create new equipment
 *     tags: [Equipment]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [tractor, combine, planter, cultivator, sprayer, tillage, harvest, other]
 *               make:
 *                 type: string
 *               model:
 *                 type: string
 *               year:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Equipment created successfully
 */
router.post('/', authenticateToken, async (req, res) => {
  try {
    const equipmentData = {
      ...req.body,
      userId: req.user.id
    };

    const equipment = await Equipment.create(equipmentData);

    res.status(201).json({
      message: 'Equipment created successfully',
      equipment
    });
  } catch (error) {
    console.error('Create equipment error:', error);
    res.status(500).json({
      error: 'Failed to create equipment'
    });
  }
});

/**
 * @swagger
 * /api/equipment/{id}:
 *   get:
 *     summary: Get equipment by ID
 *     tags: [Equipment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Equipment retrieved successfully
 *       404:
 *         description: Equipment not found
 */
router.get('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const equipment = await Equipment.findOne({
      where: { id: req.params.id, userId: req.user.id },
      include: [
        {
          model: EquipmentUsage,
          as: 'usageRecords',
          order: [['date', 'DESC']]
        }
      ]
    });

    if (!equipment) {
      return res.status(404).json({
        error: 'Equipment not found'
      });
    }

    res.json({ equipment });
  } catch (error) {
    console.error('Get equipment error:', error);
    res.status(500).json({
      error: 'Failed to retrieve equipment'
    });
  }
});

/**
 * @swagger
 * /api/equipment/{id}:
 *   put:
 *     summary: Update equipment
 *     tags: [Equipment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Equipment updated successfully
 *       404:
 *         description: Equipment not found
 */
router.put('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const equipment = await Equipment.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!equipment) {
      return res.status(404).json({
        error: 'Equipment not found'
      });
    }

    await equipment.update(req.body);

    res.json({
      message: 'Equipment updated successfully',
      equipment
    });
  } catch (error) {
    console.error('Update equipment error:', error);
    res.status(500).json({
      error: 'Failed to update equipment'
    });
  }
});

/**
 * @swagger
 * /api/equipment/{id}:
 *   delete:
 *     summary: Delete equipment
 *     tags: [Equipment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Equipment deleted successfully
 *       404:
 *         description: Equipment not found
 */
router.delete('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const equipment = await Equipment.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!equipment) {
      return res.status(404).json({
        error: 'Equipment not found'
      });
    }

    // Soft delete by setting isActive to false
    await equipment.update({ isActive: false });

    res.json({
      message: 'Equipment deleted successfully'
    });
  } catch (error) {
    console.error('Delete equipment error:', error);
    res.status(500).json({
      error: 'Failed to delete equipment'
    });
  }
});

/**
 * @swagger
 * /api/equipment/{id}/usage:
 *   post:
 *     summary: Record equipment usage
 *     tags: [Equipment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - hoursUsed
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               hoursUsed:
 *                 type: number
 *               fuelUsed:
 *                 type: number
 *               fuelCost:
 *                 type: number
 *               operationType:
 *                 type: string
 *     responses:
 *       201:
 *         description: Usage recorded successfully
 */
router.post('/:id/usage', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const equipment = await Equipment.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!equipment) {
      return res.status(404).json({
        error: 'Equipment not found'
      });
    }

    const usageData = {
      ...req.body,
      equipmentId: req.params.id,
      userId: req.user.id
    };

    const usage = await EquipmentUsage.create(usageData);

    // Update equipment total hours and costs
    const totalHours = (equipment.hoursUsed || 0) + (req.body.hoursUsed || 0);
    const totalFuelCost = (equipment.fuelCost || 0) + (req.body.fuelCost || 0);

    await equipment.update({
      hoursUsed: totalHours,
      fuelCost: totalFuelCost
    });

    res.status(201).json({
      message: 'Usage recorded successfully',
      usage
    });
  } catch (error) {
    console.error('Record usage error:', error);
    res.status(500).json({
      error: 'Failed to record usage'
    });
  }
});



module.exports = router;
