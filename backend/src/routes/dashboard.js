const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const dashboardService = require('../services/dashboardService');

const router = express.Router();

/**
 * @swagger
 * /api/dashboard:
 *   get:
 *     summary: Get dashboard data
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: year
 *         schema:
 *           type: integer
 *         description: Year for data filtering (defaults to current year)
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 */
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { year = new Date().getFullYear() } = req.query;
    const userId = req.user.id;

    const dashboardData = await dashboardService.getDashboardData(userId, year);
    res.json(dashboardData);
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({
      error: 'Failed to retrieve dashboard data',
      details: error.message
    });
  }
});

/**
 * @swagger
 * /api/dashboard/kpis:
 *   get:
 *     summary: Get key performance indicators
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: KPIs retrieved successfully
 */
router.get('/kpis', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const kpis = await dashboardService.getKPIs(userId);
    res.json(kpis);
  } catch (error) {
    console.error('KPIs error:', error);
    res.status(500).json({
      error: 'Failed to retrieve KPIs'
    });
  }
});

module.exports = router;
