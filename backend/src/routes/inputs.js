const express = require('express');
const { Op } = require('sequelize');
const { Input, Field } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { validateInput, validateUUID, validatePagination } = require('../middleware/validation');

const router = express.Router();

/**
 * @swagger
 * /api/inputs:
 *   get:
 *     summary: Get user's inputs
 *     tags: [Inputs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Number of items per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by input type
 *       - in: query
 *         name: fieldId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by field ID
 *     responses:
 *       200:
 *         description: Inputs retrieved successfully
 */
// Specific routes must come before parameterized routes
/**
 * @swagger
 * /api/inputs/summary/by-type:
 *   get:
 *     summary: Get input summary by type
 *     tags: [Inputs]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Input summary retrieved successfully
 */
router.get('/summary/by-type', authenticateToken, async (req, res) => {
  try {
    const { year = new Date().getFullYear() } = req.query;
    const startDate = new Date(`${year}-01-01`);
    const endDate = new Date(`${year}-12-31`);

    const summary = await Input.findAll({
      where: {
        userId: req.user.id,
        applicationDate: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: [
        'type',
        [sequelize.fn('SUM', sequelize.col('quantity')), 'totalQuantity'],
        [sequelize.fn('SUM', sequelize.literal('quantity * "costPerUnit"')), 'totalCost'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'applicationCount']
      ],
      group: ['type'],
      order: [['type', 'ASC']]
    });

    res.json({ summary, year: parseInt(year) });
  } catch (error) {
    console.error('Error fetching input summary:', error);
    res.status(500).json({
      error: 'Failed to fetch input summary',
      details: error.message
    });
  }
});

router.get('/', authenticateToken, validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 20, type, fieldId, startDate, endDate } = req.query;
    const offset = (page - 1) * limit;

    // Build where clause
    const where = { userId: req.user.id };
    if (type) where.type = type;
    if (fieldId) where.fieldId = fieldId;
    if (startDate || endDate) {
      where.applicationDate = {};
      if (startDate) where.applicationDate[Op.gte] = startDate;
      if (endDate) where.applicationDate[Op.lte] = endDate;
    }

    const { count, rows: inputs } = await Input.findAndCountAll({
      where,
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ],
      order: [['applicationDate', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      inputs,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get inputs error:', error);
    res.status(500).json({
      error: 'Failed to retrieve inputs'
    });
  }
});

/**
 * @swagger
 * /api/inputs:
 *   post:
 *     summary: Create new input
 *     tags: [Inputs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - name
 *               - quantity
 *               - unit
 *               - costPerUnit
 *               - applicationDate
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [fertilizer, pesticide, herbicide, fungicide, insecticide, seed, other]
 *               name:
 *                 type: string
 *               quantity:
 *                 type: number
 *               unit:
 *                 type: string
 *               costPerUnit:
 *                 type: number
 *               applicationDate:
 *                 type: string
 *                 format: date
 *               fieldId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       201:
 *         description: Input created successfully
 */
router.post('/', authenticateToken, validateInput, async (req, res) => {
  try {
    const inputData = {
      ...req.body,
      userId: req.user.id
    };

    // Validate field ownership if fieldId is provided
    if (inputData.fieldId) {
      const field = await Field.findOne({
        where: { id: inputData.fieldId, userId: req.user.id }
      });
      if (!field) {
        return res.status(400).json({
          error: 'Invalid field ID or field does not belong to user'
        });
      }
    }

    const input = await Input.create(inputData);

    // Include field data in response
    const inputWithField = await Input.findByPk(input.id, {
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ]
    });

    res.status(201).json({
      message: 'Input created successfully',
      input: inputWithField
    });
  } catch (error) {
    console.error('Create input error:', error);
    res.status(500).json({
      error: 'Failed to create input'
    });
  }
});

/**
 * @swagger
 * /api/inputs/{id}:
 *   get:
 *     summary: Get input by ID
 *     tags: [Inputs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Input retrieved successfully
 *       404:
 *         description: Input not found
 */
router.get('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const input = await Input.findOne({
      where: { id: req.params.id, userId: req.user.id },
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ]
    });

    if (!input) {
      return res.status(404).json({
        error: 'Input not found'
      });
    }

    res.json({ input });
  } catch (error) {
    console.error('Get input error:', error);
    res.status(500).json({
      error: 'Failed to retrieve input'
    });
  }
});

/**
 * @swagger
 * /api/inputs/{id}:
 *   put:
 *     summary: Update input
 *     tags: [Inputs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Input updated successfully
 *       404:
 *         description: Input not found
 */
router.put('/:id', authenticateToken, validateUUID('id'), validateInput, async (req, res) => {
  try {
    const input = await Input.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!input) {
      return res.status(404).json({
        error: 'Input not found'
      });
    }

    // Validate field ownership if fieldId is being updated
    if (req.body.fieldId && req.body.fieldId !== input.fieldId) {
      const field = await Field.findOne({
        where: { id: req.body.fieldId, userId: req.user.id }
      });
      if (!field) {
        return res.status(400).json({
          error: 'Invalid field ID or field does not belong to user'
        });
      }
    }

    await input.update(req.body);

    // Include field data in response
    const updatedInput = await Input.findByPk(input.id, {
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name', 'acres']
        }
      ]
    });

    res.json({
      message: 'Input updated successfully',
      input: updatedInput
    });
  } catch (error) {
    console.error('Update input error:', error);
    res.status(500).json({
      error: 'Failed to update input'
    });
  }
});

/**
 * @swagger
 * /api/inputs/{id}:
 *   delete:
 *     summary: Delete input
 *     tags: [Inputs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Input deleted successfully
 *       404:
 *         description: Input not found
 */
router.delete('/:id', authenticateToken, validateUUID('id'), async (req, res) => {
  try {
    const input = await Input.findOne({
      where: { id: req.params.id, userId: req.user.id }
    });

    if (!input) {
      return res.status(404).json({
        error: 'Input not found'
      });
    }

    await input.destroy();

    res.json({
      message: 'Input deleted successfully'
    });
  } catch (error) {
    console.error('Delete input error:', error);
    res.status(500).json({
      error: 'Failed to delete input'
    });
  }
});



module.exports = router;
