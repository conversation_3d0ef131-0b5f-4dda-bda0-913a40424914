const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = null;
    this.initializeTransporter();
  }

  initializeTransporter() {
    if (process.env.EMAIL_HOST && process.env.EMAIL_USER && process.env.EMAIL_PASSWORD) {
      this.transporter = nodemailer.createTransporter({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT || 587,
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD,
        },
      });
    } else {
      console.warn('Email configuration not found. Email features will be disabled.');
    }
  }

  async sendEmail(to, subject, html, text = null) {
    if (!this.transporter) {
      console.warn('Email transporter not configured. Skipping email send.');
      return { success: false, error: 'Email not configured' };
    }

    try {
      const mailOptions = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to,
        subject,
        html,
        text: text || this.stripHtml(html),
      };

      const info = await this.transporter.sendMail(mailOptions);
      console.log('Email sent:', info.messageId);
      return { success: true, messageId: info.messageId };
    } catch (error) {
      console.error('Email send error:', error);
      return { success: false, error: error.message };
    }
  }

  stripHtml(html) {
    return html.replace(/<[^>]*>/g, '');
  }

  async sendWelcomeEmail(user) {
    const subject = 'Welcome to Harvest Profit Pro!';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Welcome to Harvest Profit Pro!</h1>
        <p>Hello ${user.firstName},</p>
        <p>Thank you for joining Harvest Profit Pro, the comprehensive farm finance management system.</p>
        <p>Your account has been successfully created. You can now:</p>
        <ul>
          <li>Track your farm expenses</li>
          <li>Analyze field profitability</li>
          <li>Manage equipment and inputs</li>
          <li>Generate detailed reports</li>
        </ul>
        <p>If you have any questions, please don't hesitate to contact our support team.</p>
        <p>Happy farming!</p>
        <p>The Harvest Profit Pro Team</p>
      </div>
    `;

    return this.sendEmail(user.email, subject, html);
  }

  async sendPasswordResetEmail(user, resetToken) {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/reset-password?token=${resetToken}`;
    const subject = 'Password Reset Request - Harvest Profit Pro';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Password Reset Request</h1>
        <p>Hello ${user.firstName},</p>
        <p>We received a request to reset your password for your Harvest Profit Pro account.</p>
        <p>Click the button below to reset your password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
        </div>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><a href="${resetUrl}">${resetUrl}</a></p>
        <p>This link will expire in 1 hour for security reasons.</p>
        <p>If you didn't request this password reset, please ignore this email.</p>
        <p>The Harvest Profit Pro Team</p>
      </div>
    `;

    return this.sendEmail(user.email, subject, html);
  }

  async sendEmailVerification(user, verificationToken) {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/verify-email?token=${verificationToken}`;
    const subject = 'Verify Your Email - Harvest Profit Pro';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Verify Your Email Address</h1>
        <p>Hello ${user.firstName},</p>
        <p>Thank you for registering with Harvest Profit Pro. To complete your registration, please verify your email address.</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Verify Email</a>
        </div>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p><a href="${verificationUrl}">${verificationUrl}</a></p>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't create this account, please ignore this email.</p>
        <p>The Harvest Profit Pro Team</p>
      </div>
    `;

    return this.sendEmail(user.email, subject, html);
  }

  async sendLowCapitalAlert(user, currentCapital, threshold) {
    const subject = 'Low Working Capital Alert - Harvest Profit Pro';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #F44336;">Low Working Capital Alert</h1>
        <p>Hello ${user.firstName},</p>
        <p>This is an automated alert from your Harvest Profit Pro account.</p>
        <p><strong>Your working capital has fallen below the threshold:</strong></p>
        <ul>
          <li>Current Capital: $${currentCapital.toLocaleString()}</li>
          <li>Alert Threshold: $${threshold.toLocaleString()}</li>
        </ul>
        <p>We recommend reviewing your cash flow and considering the following actions:</p>
        <ul>
          <li>Review upcoming expenses and prioritize essential costs</li>
          <li>Consider accelerating receivables collection</li>
          <li>Explore short-term financing options if needed</li>
          <li>Review and optimize operational expenses</li>
        </ul>
        <p>Log in to your Harvest Profit Pro dashboard to view detailed financial reports and recommendations.</p>
        <p>The Harvest Profit Pro Team</p>
      </div>
    `;

    return this.sendEmail(user.email, subject, html);
  }
}

module.exports = new EmailService();
