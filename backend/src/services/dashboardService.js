const { Op } = require('sequelize');
const { User, Expense, Field, Input, Equipment, GrainContract } = require('../models');

class DashboardService {
  async getDashboardData(userId, year = new Date().getFullYear()) {
    try {
      // Date range for the year
      const startDate = new Date(`${year}-01-01`);
      const endDate = new Date(`${year}-12-31`);

      // Get all data in parallel for better performance
      const [
        totalExpenses,
        totalRevenue,
        expensesByCategory,
        monthlyExpenses,
        monthlyRevenue,
        fieldProfitability,
        recentExpenses,
        equipmentAlerts,
        totalFields,
        totalAcres,
        grainContracts
      ] = await Promise.all([
        this.getTotalExpenses(userId, startDate, endDate),
        this.getTotalRevenue(userId),
        this.getExpensesByCategory(userId, startDate, endDate),
        this.getMonthlyExpenses(userId, startDate, endDate),
        this.getMonthlyRevenue(userId, startDate, endDate),
        this.getFieldProfitability(userId),
        this.getRecentExpenses(userId),
        this.getEquipmentAlerts(userId),
        this.getTotalFields(userId),
        this.getTotalAcres(userId),
        this.getGrainContractsSummary(userId)
      ]);

      // Calculate derived metrics
      const netProfit = totalRevenue - totalExpenses;
      const avgProfitPerAcre = totalAcres > 0 ? netProfit / totalAcres : 0;
      const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

      // Get top and bottom performing fields
      const topFields = fieldProfitability
        .filter(field => field.profitPerAcre > 0)
        .slice(0, 5);
      
      const bottomFields = fieldProfitability
        .filter(field => field.profitPerAcre < 0)
        .slice(-5);

      return {
        summary: {
          totalExpenses,
          totalRevenue,
          netProfit,
          totalFields,
          totalAcres,
          avgProfitPerAcre,
          profitMargin
        },
        charts: {
          expensesByCategory,
          monthlyExpenses,
          monthlyRevenue,
          profitabilityTrend: this.calculateProfitabilityTrend(monthlyRevenue, monthlyExpenses)
        },
        fields: {
          all: fieldProfitability,
          topPerforming: topFields,
          needingAttention: bottomFields
        },
        recentActivity: {
          expenses: recentExpenses
        },
        alerts: {
          equipment: equipmentAlerts,
          lowProfitFields: bottomFields.length,
          openContracts: grainContracts.open,
          overdueContracts: grainContracts.overdue
        }
      };
    } catch (error) {
      console.error('Dashboard service error:', error);
      throw error;
    }
  }

  async getTotalExpenses(userId, startDate, endDate) {
    const result = await Expense.sum('amount', {
      where: {
        userId,
        date: {
          [Op.between]: [startDate, endDate]
        }
      }
    });
    return result || 0;
  }

  async getTotalRevenue(userId) {
    const result = await Field.sum('revenue', {
      where: { userId, isActive: true }
    });
    return result || 0;
  }

  async getExpensesByCategory(userId, startDate, endDate) {
    const expenses = await Expense.findAll({
      where: {
        userId,
        date: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: [
        'category',
        [require('sequelize').fn('SUM', require('sequelize').col('amount')), 'total']
      ],
      group: ['category'],
      order: [[require('sequelize').literal('total'), 'DESC']]
    });

    return expenses.map(item => ({
      category: item.category,
      amount: parseFloat(item.dataValues.total)
    }));
  }

  async getMonthlyExpenses(userId, startDate, endDate) {
    const expenses = await Expense.findAll({
      where: {
        userId,
        date: {
          [Op.between]: [startDate, endDate]
        }
      },
      attributes: [
        [require('sequelize').fn('DATE_TRUNC', 'month', require('sequelize').col('date')), 'month'],
        [require('sequelize').fn('SUM', require('sequelize').col('amount')), 'total']
      ],
      group: [require('sequelize').fn('DATE_TRUNC', 'month', require('sequelize').col('date'))],
      order: [[require('sequelize').fn('DATE_TRUNC', 'month', require('sequelize').col('date')), 'ASC']]
    });

    return expenses.map(item => ({
      month: item.dataValues.month,
      amount: parseFloat(item.dataValues.total)
    }));
  }

  async getMonthlyRevenue(userId, startDate, endDate) {
    // For now, we'll distribute annual revenue across months
    // In a real system, you'd track monthly revenue separately
    const totalRevenue = await this.getTotalRevenue(userId);
    const monthlyRevenue = totalRevenue / 12;

    const months = [];
    for (let i = 0; i < 12; i++) {
      const month = new Date(startDate.getFullYear(), i, 1);
      months.push({
        month,
        amount: monthlyRevenue
      });
    }

    return months;
  }

  calculateProfitabilityTrend(monthlyRevenue, monthlyExpenses) {
    const trend = [];
    
    // Create a map of expenses by month for easier lookup
    const expenseMap = new Map();
    monthlyExpenses.forEach(item => {
      const monthKey = new Date(item.month).toISOString().substring(0, 7);
      expenseMap.set(monthKey, item.amount);
    });

    monthlyRevenue.forEach(revenueItem => {
      const monthKey = new Date(revenueItem.month).toISOString().substring(0, 7);
      const expenses = expenseMap.get(monthKey) || 0;
      const profit = revenueItem.amount - expenses;

      trend.push({
        month: revenueItem.month,
        revenue: revenueItem.amount,
        expenses,
        profit
      });
    });

    return trend;
  }

  async getFieldProfitability(userId) {
    const fields = await Field.findAll({
      where: { userId, isActive: true },
      attributes: [
        'id',
        'name',
        'acres',
        'revenue',
        'totalExpenses',
        'netProfit',
        'profitPerAcre',
        'currentCrop'
      ],
      order: [['profitPerAcre', 'DESC']]
    });

    return fields.map(field => field.toJSON());
  }

  async getRecentExpenses(userId, limit = 10) {
    const expenses = await Expense.findAll({
      where: { userId },
      include: [
        {
          model: Field,
          as: 'field',
          attributes: ['id', 'name']
        }
      ],
      order: [['date', 'DESC'], ['createdAt', 'DESC']],
      limit
    });

    return expenses.map(expense => expense.toJSON());
  }

  async getEquipmentAlerts(userId) {
    const alerts = await Equipment.findAll({
      where: {
        userId,
        isActive: true,
        nextMaintenanceDate: {
          [Op.lte]: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // Next 30 days
        }
      },
      attributes: ['id', 'name', 'type', 'nextMaintenanceDate', 'hoursUsed'],
      order: [['nextMaintenanceDate', 'ASC']]
    });

    return alerts.map(alert => alert.toJSON());
  }

  async getTotalFields(userId) {
    return await Field.count({
      where: { userId, isActive: true }
    });
  }

  async getTotalAcres(userId) {
    const result = await Field.sum('acres', {
      where: { userId, isActive: true }
    });
    return result || 0;
  }

  async getGrainContractsSummary(userId) {
    const [open, overdue] = await Promise.all([
      GrainContract.count({
        where: {
          userId,
          status: ['open', 'partially_delivered']
        }
      }),
      GrainContract.count({
        where: {
          userId,
          status: ['open', 'partially_delivered'],
          deliveryEndDate: {
            [Op.lt]: new Date()
          }
        }
      })
    ]);

    return { open, overdue };
  }

  async getKPIs(userId) {
    const currentYear = new Date().getFullYear();
    const lastYear = currentYear - 1;

    const [currentData, lastYearData] = await Promise.all([
      this.getDashboardData(userId, currentYear),
      this.getDashboardData(userId, lastYear)
    ]);

    const calculateChange = (current, previous) => {
      if (previous === 0) return 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      kpis: [
        {
          title: 'Total Expenses',
          value: currentData.summary.totalExpenses,
          change: calculateChange(
            currentData.summary.totalExpenses,
            lastYearData.summary.totalExpenses
          ),
          format: 'currency'
        },
        {
          title: 'Total Revenue',
          value: currentData.summary.totalRevenue,
          change: calculateChange(
            currentData.summary.totalRevenue,
            lastYearData.summary.totalRevenue
          ),
          format: 'currency'
        },
        {
          title: 'Net Profit',
          value: currentData.summary.netProfit,
          change: calculateChange(
            currentData.summary.netProfit,
            lastYearData.summary.netProfit
          ),
          format: 'currency'
        },
        {
          title: 'Profit Margin',
          value: currentData.summary.profitMargin,
          change: calculateChange(
            currentData.summary.profitMargin,
            lastYearData.summary.profitMargin
          ),
          format: 'percentage'
        }
      ]
    };
  }
}

module.exports = new DashboardService();
