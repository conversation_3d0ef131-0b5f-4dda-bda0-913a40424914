'use strict';

const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    const hashedPassword = await bcrypt.hash('password123', 12);
    const now = new Date();

    await queryInterface.bulkInsert('users', [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        farmName: 'Demo Farm',
        phone: '******-0100',
        address: '123 Farm Road, Rural County, State 12345',
        isActive: true,
        emailVerifiedAt: now,
        createdAt: now,
        updatedAt: now
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: '<PERSON>',
        lastName: 'Farmer',
        role: 'user',
        farmName: '<PERSON> Acres',
        phone: '******-0101',
        address: '456 Country Lane, Farm Town, State 12346',
        isActive: true,
        emailVerifiedAt: now,
        createdAt: now,
        updatedAt: now
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Sarah',
        lastName: 'Manager',
        role: 'manager',
        farmName: 'Green Valley Farm',
        phone: '******-0102',
        address: '789 Agricultural Way, Crop City, State 12347',
        isActive: true,
        emailVerifiedAt: now,
        createdAt: now,
        updatedAt: now
      }
    ], {});
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', null, {});
  }
};
