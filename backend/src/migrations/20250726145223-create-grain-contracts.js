'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('grain_contracts', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      contractNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      grainType: {
        type: Sequelize.ENUM('corn', 'soybeans', 'wheat', 'oats', 'barley', 'rice', 'sorghum', 'other'),
        allowNull: false
      },
      quantity: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false
      },
      unit: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'bushels'
      },
      pricePerUnit: {
        type: Sequelize.DECIMAL(10, 4),
        allowNull: false
      },
      totalValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false
      },
      contractType: {
        type: Sequelize.ENUM('cash', 'forward', 'basis', 'hedge_to_arrive', 'minimum_price'),
        allowNull: false
      },
      buyer: {
        type: Sequelize.STRING,
        allowNull: false
      },
      deliveryLocation: {
        type: Sequelize.STRING,
        allowNull: false
      },
      contractDate: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      deliveryStartDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      deliveryEndDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      deliveredQuantity: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
        defaultValue: 0
      },
      remainingQuantity: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      status: {
        type: Sequelize.ENUM('open', 'partially_delivered', 'completed', 'cancelled'),
        defaultValue: 'open'
      },
      qualitySpecs: {
        type: Sequelize.JSON,
        allowNull: true
      },
      premiumsDiscounts: {
        type: Sequelize.JSON,
        allowNull: true
      },
      basisLevel: {
        type: Sequelize.DECIMAL(6, 4),
        allowNull: true
      },
      futuresMonth: {
        type: Sequelize.STRING,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('grain_contracts', ['userId']);
    await queryInterface.addIndex('grain_contracts', ['grainType']);
    await queryInterface.addIndex('grain_contracts', ['status']);
    await queryInterface.addIndex('grain_contracts', ['contractDate']);
    await queryInterface.addIndex('grain_contracts', ['deliveryStartDate']);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('grain_contracts');
  }
};
