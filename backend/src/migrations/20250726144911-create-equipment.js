'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('equipment', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      type: {
        type: Sequelize.ENUM('tractor', 'combine', 'planter', 'cultivator', 'sprayer', 'tillage', 'harvest', 'other'),
        allowNull: false
      },
      make: {
        type: Sequelize.STRING,
        allowNull: true
      },
      model: {
        type: Sequelize.STRING,
        allowNull: true
      },
      year: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      serialNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      purchaseDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      purchasePrice: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      currentValue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      hoursUsed: {
        type: Sequelize.DECIMAL(10, 1),
        allowNull: true,
        defaultValue: 0
      },
      maintenanceCost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        defaultValue: 0
      },
      fuelCost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        defaultValue: 0
      },
      insuranceCost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        defaultValue: 0
      },
      lastMaintenanceDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      nextMaintenanceDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      maintenanceInterval: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      location: {
        type: Sequelize.STRING,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('equipment', ['userId']);
    await queryInterface.addIndex('equipment', ['type']);
    await queryInterface.addIndex('equipment', ['isActive']);
    await queryInterface.addIndex('equipment', ['nextMaintenanceDate']);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('equipment');
  }
};
