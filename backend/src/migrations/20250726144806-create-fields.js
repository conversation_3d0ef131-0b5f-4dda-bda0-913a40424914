'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('fields', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      acres: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      location: {
        type: Sequelize.STRING,
        allowNull: true
      },
      coordinates: {
        type: Sequelize.JSON,
        allowNull: true
      },
      soilType: {
        type: Sequelize.STRING,
        allowNull: true
      },
      soilPh: {
        type: Sequelize.DECIMAL(3, 1),
        allowNull: true
      },
      drainageClass: {
        type: Sequelize.ENUM('well_drained', 'moderately_drained', 'poorly_drained', 'very_poorly_drained'),
        allowNull: true
      },
      irrigated: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      irrigationType: {
        type: Sequelize.ENUM('center_pivot', 'linear_move', 'drip', 'flood', 'sprinkler', 'other'),
        allowNull: true
      },
      currentCrop: {
        type: Sequelize.STRING,
        allowNull: true
      },
      previousCrop: {
        type: Sequelize.STRING,
        allowNull: true
      },
      plantingDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      harvestDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      expectedYield: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      actualYield: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      yieldUnit: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: 'bushels'
      },
      revenue: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        defaultValue: 0
      },
      totalExpenses: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        defaultValue: 0
      },
      netProfit: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        defaultValue: 0
      },
      profitPerAcre: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
        defaultValue: 0
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('fields', ['userId']);
    await queryInterface.addIndex('fields', ['currentCrop']);
    await queryInterface.addIndex('fields', ['isActive']);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('fields');
  }
};
