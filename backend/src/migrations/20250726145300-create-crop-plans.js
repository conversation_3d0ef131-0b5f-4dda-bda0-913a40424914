'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('crop_plans', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      fieldId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'fields',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      year: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      cropType: {
        type: Sequelize.STRING,
        allowNull: false
      },
      variety: {
        type: Sequelize.STRING,
        allowNull: true
      },
      acres: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      plannedPlantingDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      actualPlantingDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      plannedHarvestDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      actualHarvestDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      estimatedYield: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      actualYield: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      yieldUnit: {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: 'bushels'
      },
      estimatedPrice: {
        type: Sequelize.DECIMAL(10, 4),
        allowNull: true
      },
      actualPrice: {
        type: Sequelize.DECIMAL(10, 4),
        allowNull: true
      },
      estimatedRevenue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      actualRevenue: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      estimatedExpenses: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      actualExpenses: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      estimatedProfit: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      actualProfit: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      rotationPosition: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      previousCrop: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nextCrop: {
        type: Sequelize.STRING,
        allowNull: true
      },
      insuranceCoverage: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: true
      },
      insurancePremium: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('crop_plans', ['userId']);
    await queryInterface.addIndex('crop_plans', ['fieldId']);
    await queryInterface.addIndex('crop_plans', ['year']);
    await queryInterface.addIndex('crop_plans', ['cropType']);
    await queryInterface.addIndex('crop_plans', ['fieldId', 'year'], { unique: true });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('crop_plans');
  }
};
