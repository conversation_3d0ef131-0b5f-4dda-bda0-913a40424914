'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('inputs', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      fieldId: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'fields',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      type: {
        type: Sequelize.ENUM('fertilizer', 'pesticide', 'herbicide', 'fungicide', 'insecticide', 'seed', 'other'),
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      brand: {
        type: Sequelize.STRING,
        allowNull: true
      },
      activeIngredient: {
        type: Sequelize.STRING,
        allowNull: true
      },
      concentration: {
        type: Sequelize.STRING,
        allowNull: true
      },
      quantity: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      unit: {
        type: Sequelize.STRING,
        allowNull: false
      },
      costPerUnit: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      totalCost: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      applicationDate: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      applicationMethod: {
        type: Sequelize.ENUM('broadcast', 'banded', 'foliar', 'soil_injection', 'seed_treatment', 'other'),
        allowNull: true
      },
      applicationRate: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      applicationRateUnit: {
        type: Sequelize.STRING,
        allowNull: true
      },
      acresApplied: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true
      },
      weatherConditions: {
        type: Sequelize.JSON,
        allowNull: true
      },
      vendor: {
        type: Sequelize.STRING,
        allowNull: true
      },
      lotNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      expirationDate: {
        type: Sequelize.DATEONLY,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('inputs', ['userId']);
    await queryInterface.addIndex('inputs', ['fieldId']);
    await queryInterface.addIndex('inputs', ['type']);
    await queryInterface.addIndex('inputs', ['applicationDate']);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('inputs');
  }
};
