module.exports = (sequelize, DataTypes) => {
  const Equipment = sequelize.define('Equipment', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    type: {
      type: DataTypes.ENUM('tractor', 'combine', 'planter', 'cultivator', 'sprayer', 'tillage', 'harvest', 'other'),
      allowNull: false
    },
    make: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    model: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1900,
        max: new Date().getFullYear() + 1
      }
    },
    serialNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    purchaseDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    purchasePrice: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    currentValue: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    hoursUsed: {
      type: DataTypes.DECIMAL(10, 1),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    maintenanceCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    fuelCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    insuranceCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    lastMaintenanceDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    nextMaintenanceDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    maintenanceInterval: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Hours between maintenance'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 200]
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'equipment',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['type']
      },
      {
        fields: ['isActive']
      },
      {
        fields: ['nextMaintenanceDate']
      }
    ]
  });

  Equipment.associate = function(models) {
    Equipment.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Equipment.hasMany(models.EquipmentUsage, {
      foreignKey: 'equipmentId',
      as: 'usageRecords'
    });
  };

  return Equipment;
};
