module.exports = (sequelize, DataTypes) => {
  const Field = sequelize.define('Field', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    acres: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    location: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 200]
      }
    },
    coordinates: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'GeoJSON polygon coordinates'
    },
    soilType: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    soilPh: {
      type: DataTypes.DECIMAL(3, 1),
      allowNull: true,
      validate: {
        min: 0,
        max: 14
      }
    },
    drainageClass: {
      type: DataTypes.ENUM('well_drained', 'moderately_drained', 'poorly_drained', 'very_poorly_drained'),
      allowNull: true
    },
    irrigated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    irrigationType: {
      type: DataTypes.ENUM('center_pivot', 'linear_move', 'drip', 'flood', 'sprinkler', 'other'),
      allowNull: true
    },
    currentCrop: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    previousCrop: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    plantingDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    harvestDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    expectedYield: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    actualYield: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    yieldUnit: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'bushels',
      validate: {
        len: [0, 20]
      }
    },
    revenue: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    totalExpenses: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    netProfit: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0
    },
    profitPerAcre: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'fields',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['currentCrop']
      },
      {
        fields: ['isActive']
      }
    ],
    hooks: {
      beforeSave: (field) => {
        // Calculate profit metrics
        if (field.revenue !== null && field.totalExpenses !== null) {
          field.netProfit = field.revenue - field.totalExpenses;
          if (field.acres > 0) {
            field.profitPerAcre = field.netProfit / field.acres;
          }
        }
      }
    }
  });

  Field.associate = function(models) {
    Field.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Field.hasMany(models.Expense, {
      foreignKey: 'fieldId',
      as: 'expenses'
    });
    Field.hasMany(models.Input, {
      foreignKey: 'fieldId',
      as: 'inputs'
    });
    Field.hasMany(models.CropPlan, {
      foreignKey: 'fieldId',
      as: 'cropPlans'
    });
  };

  return Field;
};
