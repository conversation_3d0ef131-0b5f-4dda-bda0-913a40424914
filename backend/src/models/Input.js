module.exports = (sequelize, DataTypes) => {
  const Input = sequelize.define('Input', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    fieldId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'fields',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.ENUM('fertilizer', 'pesticide', 'herbicide', 'fungicide', 'insecticide', 'seed', 'other'),
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    brand: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    activeIngredient: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 200]
      }
    },
    concentration: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 20]
      }
    },
    costPerUnit: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    totalCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    applicationDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    applicationMethod: {
      type: DataTypes.ENUM('broadcast', 'banded', 'foliar', 'soil_injection', 'seed_treatment', 'other'),
      allowNull: true
    },
    applicationRate: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    applicationRateUnit: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 30]
      }
    },
    acresApplied: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    weatherConditions: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Temperature, humidity, wind speed, etc.'
    },
    vendor: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    lotNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    expirationDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'inputs',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['fieldId']
      },
      {
        fields: ['type']
      },
      {
        fields: ['applicationDate']
      }
    ],
    hooks: {
      beforeSave: (input) => {
        // Calculate total cost
        if (input.quantity && input.costPerUnit) {
          input.totalCost = input.quantity * input.costPerUnit;
        }
      }
    }
  });

  Input.associate = function(models) {
    Input.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    Input.belongsTo(models.Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });
  };

  return Input;
};
