module.exports = (sequelize, DataTypes) => {
  const EquipmentUsage = sequelize.define('EquipmentUsage', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    equipmentId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'equipment',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    fieldId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'fields',
        key: 'id'
      }
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    hoursUsed: {
      type: DataTypes.DECIMAL(8, 1),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    fuelUsed: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    fuelCost: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    operationType: {
      type: DataTypes.ENUM('planting', 'cultivation', 'spraying', 'harvesting', 'tillage', 'transport', 'maintenance', 'other'),
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'equipment_usage',
    timestamps: true,
    indexes: [
      {
        fields: ['equipmentId']
      },
      {
        fields: ['userId']
      },
      {
        fields: ['fieldId']
      },
      {
        fields: ['date']
      }
    ]
  });

  EquipmentUsage.associate = function(models) {
    EquipmentUsage.belongsTo(models.Equipment, {
      foreignKey: 'equipmentId',
      as: 'equipment'
    });
    EquipmentUsage.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    EquipmentUsage.belongsTo(models.Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });
  };

  return EquipmentUsage;
};
