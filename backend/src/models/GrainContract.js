module.exports = (sequelize, DataTypes) => {
  const GrainContract = sequelize.define('GrainContract', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    contractNumber: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [1, 50]
      }
    },
    grainType: {
      type: DataTypes.ENUM('corn', 'soybeans', 'wheat', 'oats', 'barley', 'rice', 'sorghum', 'other'),
      allowNull: false
    },
    quantity: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'bushels',
      validate: {
        len: [1, 20]
      }
    },
    pricePerUnit: {
      type: DataTypes.DECIMAL(10, 4),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    totalValue: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    contractType: {
      type: DataTypes.ENUM('cash', 'forward', 'basis', 'hedge_to_arrive', 'minimum_price'),
      allowNull: false
    },
    buyer: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    deliveryLocation: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 200]
      }
    },
    contractDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    deliveryStartDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    deliveryEndDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    deliveredQuantity: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      defaultValue: 0,
      validate: {
        min: 0
      }
    },
    remainingQuantity: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    status: {
      type: DataTypes.ENUM('open', 'partially_delivered', 'completed', 'cancelled'),
      defaultValue: 'open'
    },
    qualitySpecs: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Moisture, protein, test weight, etc.'
    },
    premiumsDiscounts: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Quality premiums and discounts'
    },
    basisLevel: {
      type: DataTypes.DECIMAL(6, 4),
      allowNull: true,
      comment: 'Basis over/under futures'
    },
    futuresMonth: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 20]
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'grain_contracts',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['grainType']
      },
      {
        fields: ['status']
      },
      {
        fields: ['contractDate']
      },
      {
        fields: ['deliveryStartDate']
      }
    ],
    hooks: {
      beforeSave: (contract) => {
        // Calculate total value
        if (contract.quantity && contract.pricePerUnit) {
          contract.totalValue = contract.quantity * contract.pricePerUnit;
        }
        // Calculate remaining quantity
        if (contract.quantity && contract.deliveredQuantity !== null) {
          contract.remainingQuantity = contract.quantity - contract.deliveredQuantity;
        }
      }
    }
  });

  GrainContract.associate = function(models) {
    GrainContract.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    GrainContract.hasMany(models.GrainDelivery, {
      foreignKey: 'contractId',
      as: 'deliveries'
    });
  };

  return GrainContract;
};
