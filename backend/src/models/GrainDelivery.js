module.exports = (sequelize, DataTypes) => {
  const GrainDelivery = sequelize.define('GrainDelivery', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    contractId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'grain_contracts',
        key: 'id'
      }
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    fieldId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'fields',
        key: 'id'
      }
    },
    deliveryDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    quantity: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'bushels'
    },
    moisture: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      }
    },
    testWeight: {
      type: DataTypes.DECIMAL(6, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    protein: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      }
    },
    dockage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      }
    },
    pricePerUnit: {
      type: DataTypes.DECIMAL(10, 4),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    totalValue: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    premiumsDiscounts: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0
    },
    netValue: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: 0
      }
    },
    ticketNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    truckNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    driverName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'grain_deliveries',
    timestamps: true,
    indexes: [
      {
        fields: ['contractId']
      },
      {
        fields: ['userId']
      },
      {
        fields: ['fieldId']
      },
      {
        fields: ['deliveryDate']
      }
    ],
    hooks: {
      beforeSave: (delivery) => {
        // Calculate total value and net value
        if (delivery.quantity && delivery.pricePerUnit) {
          delivery.totalValue = delivery.quantity * delivery.pricePerUnit;
          delivery.netValue = delivery.totalValue + (delivery.premiumsDiscounts || 0);
        }
      }
    }
  });

  GrainDelivery.associate = function(models) {
    GrainDelivery.belongsTo(models.GrainContract, {
      foreignKey: 'contractId',
      as: 'contract'
    });
    GrainDelivery.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    GrainDelivery.belongsTo(models.Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });
  };

  return GrainDelivery;
};
