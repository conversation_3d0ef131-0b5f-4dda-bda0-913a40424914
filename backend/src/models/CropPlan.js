module.exports = (sequelize, DataTypes) => {
  const CropPlan = sequelize.define('CropPlan', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    fieldId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'fields',
        key: 'id'
      }
    },
    year: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 2000,
        max: 2100
      }
    },
    cropType: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [1, 50]
      }
    },
    variety: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 100]
      }
    },
    acres: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0.01
      }
    },
    plannedPlantingDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    actualPlantingDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    plannedHarvestDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    actualHarvestDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    estimatedYield: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    actualYield: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    yieldUnit: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'bushels',
      validate: {
        len: [0, 20]
      }
    },
    estimatedPrice: {
      type: DataTypes.DECIMAL(10, 4),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    actualPrice: {
      type: DataTypes.DECIMAL(10, 4),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    estimatedRevenue: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    actualRevenue: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    estimatedExpenses: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    actualExpenses: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    estimatedProfit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true
    },
    actualProfit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true
    },
    rotationPosition: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1
      }
    },
    previousCrop: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    nextCrop: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [0, 50]
      }
    },
    insuranceCoverage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      validate: {
        min: 0,
        max: 100
      },
      comment: 'Percentage coverage'
    },
    insurancePremium: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0
      }
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'crop_plans',
    timestamps: true,
    indexes: [
      {
        fields: ['userId']
      },
      {
        fields: ['fieldId']
      },
      {
        fields: ['year']
      },
      {
        fields: ['cropType']
      },
      {
        unique: true,
        fields: ['fieldId', 'year']
      }
    ],
    hooks: {
      beforeSave: (plan) => {
        // Calculate estimated revenue and profit
        if (plan.estimatedYield && plan.estimatedPrice) {
          plan.estimatedRevenue = plan.estimatedYield * plan.estimatedPrice;
        }
        if (plan.estimatedRevenue && plan.estimatedExpenses) {
          plan.estimatedProfit = plan.estimatedRevenue - plan.estimatedExpenses;
        }
        
        // Calculate actual revenue and profit
        if (plan.actualYield && plan.actualPrice) {
          plan.actualRevenue = plan.actualYield * plan.actualPrice;
        }
        if (plan.actualRevenue && plan.actualExpenses) {
          plan.actualProfit = plan.actualRevenue - plan.actualExpenses;
        }
      }
    }
  });

  CropPlan.associate = function(models) {
    CropPlan.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
    CropPlan.belongsTo(models.Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });
  };

  return CropPlan;
};
