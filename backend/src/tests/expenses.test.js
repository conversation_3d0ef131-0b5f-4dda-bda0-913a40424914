const request = require('supertest');
const app = require('../server');
const { User, Expense, Field } = require('../models');
const { sequelize } = require('../config/database');

describe('Expense Endpoints', () => {
  let token;
  let user;
  let field;

  beforeAll(async () => {
    await sequelize.sync({ force: true });
  });

  afterAll(async () => {
    await sequelize.close();
  });

  beforeEach(async () => {
    // Clean up
    await Expense.destroy({ where: {} });
    await Field.destroy({ where: {} });
    await User.destroy({ where: {} });

    // Create test user
    const response = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'John',
        lastName: 'Doe'
      });

    token = response.body.token;
    user = response.body.user;

    // Create test field
    const fieldResponse = await request(app)
      .post('/api/fields')
      .set('Authorization', `Bearer ${token}`)
      .send({
        name: 'Test Field',
        acres: 100.5,
        location: 'Test Location'
      });

    field = fieldResponse.body.field;
  });

  describe('POST /api/expenses', () => {
    it('should create a new expense successfully', async () => {
      const expenseData = {
        category: 'fertilizer',
        description: 'Test fertilizer purchase',
        amount: 500.00,
        date: '2024-01-15',
        fieldId: field.id,
        vendor: 'Test Vendor'
      };

      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${token}`)
        .send(expenseData)
        .expect(201);

      expect(response.body).toHaveProperty('message', 'Expense created successfully');
      expect(response.body).toHaveProperty('expense');
      expect(response.body.expense.description).toBe(expenseData.description);
      expect(response.body.expense.amount).toBe(expenseData.amount.toString());
    });

    it('should return 400 for invalid category', async () => {
      const expenseData = {
        category: 'invalid_category',
        description: 'Test expense',
        amount: 100.00,
        date: '2024-01-15'
      };

      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${token}`)
        .send(expenseData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should return 400 for missing required fields', async () => {
      const expenseData = {
        category: 'fertilizer',
        // Missing description, amount, and date
      };

      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${token}`)
        .send(expenseData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
    });

    it('should return 401 without authentication', async () => {
      const expenseData = {
        category: 'fertilizer',
        description: 'Test expense',
        amount: 100.00,
        date: '2024-01-15'
      };

      const response = await request(app)
        .post('/api/expenses')
        .send(expenseData)
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Access token required');
    });
  });

  describe('GET /api/expenses', () => {
    beforeEach(async () => {
      // Create test expenses
      await Expense.bulkCreate([
        {
          userId: user.id,
          fieldId: field.id,
          category: 'fertilizer',
          description: 'Fertilizer 1',
          amount: 100.00,
          date: '2024-01-15'
        },
        {
          userId: user.id,
          fieldId: field.id,
          category: 'seed',
          description: 'Seed 1',
          amount: 200.00,
          date: '2024-01-16'
        },
        {
          userId: user.id,
          category: 'fuel',
          description: 'Fuel 1',
          amount: 150.00,
          date: '2024-01-17'
        }
      ]);
    });

    it('should return user expenses with pagination', async () => {
      const response = await request(app)
        .get('/api/expenses')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('expenses');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.expenses).toHaveLength(3);
      expect(response.body.pagination.total).toBe(3);
    });

    it('should filter expenses by category', async () => {
      const response = await request(app)
        .get('/api/expenses?category=fertilizer')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body.expenses).toHaveLength(1);
      expect(response.body.expenses[0].category).toBe('fertilizer');
    });

    it('should filter expenses by field', async () => {
      const response = await request(app)
        .get(`/api/expenses?fieldId=${field.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body.expenses).toHaveLength(2);
      response.body.expenses.forEach(expense => {
        expect(expense.fieldId).toBe(field.id);
      });
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .get('/api/expenses')
        .expect(401);

      expect(response.body).toHaveProperty('error', 'Access token required');
    });
  });

  describe('PUT /api/expenses/:id', () => {
    let expense;

    beforeEach(async () => {
      expense = await Expense.create({
        userId: user.id,
        fieldId: field.id,
        category: 'fertilizer',
        description: 'Original description',
        amount: 100.00,
        date: '2024-01-15'
      });
    });

    it('should update expense successfully', async () => {
      const updateData = {
        description: 'Updated description',
        amount: 150.00
      };

      const response = await request(app)
        .put(`/api/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${token}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Expense updated successfully');
      expect(response.body.expense.description).toBe(updateData.description);
      expect(response.body.expense.amount).toBe(updateData.amount.toString());
    });

    it('should return 404 for non-existent expense', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-426614174000';
      
      const response = await request(app)
        .put(`/api/expenses/${fakeId}`)
        .set('Authorization', `Bearer ${token}`)
        .send({ description: 'Updated' })
        .expect(404);

      expect(response.body).toHaveProperty('error', 'Expense not found');
    });
  });

  describe('DELETE /api/expenses/:id', () => {
    let expense;

    beforeEach(async () => {
      expense = await Expense.create({
        userId: user.id,
        fieldId: field.id,
        category: 'fertilizer',
        description: 'Test expense',
        amount: 100.00,
        date: '2024-01-15'
      });
    });

    it('should delete expense successfully', async () => {
      const response = await request(app)
        .delete(`/api/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Expense deleted successfully');

      // Verify expense is deleted
      const deletedExpense = await Expense.findByPk(expense.id);
      expect(deletedExpense).toBeNull();
    });

    it('should return 404 for non-existent expense', async () => {
      const fakeId = '123e4567-e89b-12d3-a456-426614174000';
      
      const response = await request(app)
        .delete(`/api/expenses/${fakeId}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(404);

      expect(response.body).toHaveProperty('error', 'Expense not found');
    });
  });
});
