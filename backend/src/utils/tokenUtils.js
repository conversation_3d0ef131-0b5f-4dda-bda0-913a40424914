const crypto = require('crypto');
const jwt = require('jsonwebtoken');

class TokenUtils {
  // Generate a secure random token for email verification, password reset, etc.
  generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  // Generate JWT access token
  generateAccessToken(payload) {
    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    });
  }

  // Generate JWT refresh token
  generateRefreshToken(payload) {
    return jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
      expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
    });
  }

  // Verify JWT token
  verifyToken(token, secret = process.env.JWT_SECRET) {
    try {
      return jwt.verify(token, secret);
    } catch (error) {
      throw new Error('Invalid or expired token');
    }
  }

  // Verify refresh token
  verifyRefreshToken(token) {
    return this.verifyToken(token, process.env.JWT_REFRESH_SECRET);
  }

  // Generate password reset token with expiration
  generatePasswordResetToken() {
    const token = this.generateSecureToken();
    const expires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
    return { token, expires };
  }

  // Generate email verification token with expiration
  generateEmailVerificationToken() {
    const token = this.generateSecureToken();
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
    return { token, expires };
  }

  // Hash token for secure storage
  hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  // Verify hashed token
  verifyHashedToken(token, hashedToken) {
    const hash = this.hashToken(token);
    return hash === hashedToken;
  }

  // Generate API key
  generateApiKey() {
    const prefix = 'hpp_'; // Harvest Profit Pro prefix
    const key = this.generateSecureToken(24);
    return prefix + key;
  }

  // Validate API key format
  isValidApiKeyFormat(apiKey) {
    return /^hpp_[a-f0-9]{48}$/.test(apiKey);
  }
}

module.exports = new TokenUtils();
