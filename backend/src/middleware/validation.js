const { body, param, query, validationResult } = require('express-validator');

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// User validation rules
const validateUserRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('firstName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name is required and must be less than 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name is required and must be less than 50 characters'),
  body('farmName')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Farm name must be less than 100 characters'),
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Invalid phone number format'),
  handleValidationErrors
];

const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  handleValidationErrors
];

// Expense validation rules
const validateExpense = [
  body('category')
    .isIn(['seed', 'fertilizer', 'pesticide', 'fuel', 'equipment', 'labor', 'insurance', 'utilities', 'maintenance', 'transportation', 'storage', 'marketing', 'professional_services', 'other'])
    .withMessage('Invalid expense category'),
  body('description')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Description is required and must be less than 500 characters'),
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('date')
    .isISO8601()
    .toDate()
    .withMessage('Valid date is required'),
  body('quantity')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Quantity must be a positive number'),
  body('vendor')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Vendor name must be less than 100 characters'),
  handleValidationErrors
];

// Field validation rules
const validateField = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Field name is required and must be less than 100 characters'),
  body('acres')
    .isFloat({ min: 0.01 })
    .withMessage('Acres must be greater than 0'),
  body('location')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Location must be less than 200 characters'),
  body('soilPh')
    .optional()
    .isFloat({ min: 0, max: 14 })
    .withMessage('Soil pH must be between 0 and 14'),
  body('currentCrop')
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage('Current crop must be less than 50 characters'),
  handleValidationErrors
];

// Input validation rules
const validateInput = [
  body('type')
    .isIn(['fertilizer', 'pesticide', 'herbicide', 'fungicide', 'insecticide', 'seed', 'other'])
    .withMessage('Invalid input type'),
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Input name is required and must be less than 100 characters'),
  body('quantity')
    .isFloat({ min: 0 })
    .withMessage('Quantity must be a positive number'),
  body('unit')
    .trim()
    .isLength({ min: 1, max: 20 })
    .withMessage('Unit is required and must be less than 20 characters'),
  body('costPerUnit')
    .isFloat({ min: 0 })
    .withMessage('Cost per unit must be a positive number'),
  body('applicationDate')
    .isISO8601()
    .toDate()
    .withMessage('Valid application date is required'),
  handleValidationErrors
];

// Common validation rules
const validateUUID = (field) => [
  param(field)
    .isUUID()
    .withMessage(`${field} must be a valid UUID`),
  handleValidationErrors
];

const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  handleValidationErrors
];

module.exports = {
  handleValidationErrors,
  validateUserRegistration,
  validateUserLogin,
  validateExpense,
  validateField,
  validateInput,
  validateUUID,
  validatePagination
};
