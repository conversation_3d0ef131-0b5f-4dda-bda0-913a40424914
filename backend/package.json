{"name": "harvest-profit-pro-backend", "version": "1.0.0", "description": "Backend API for Harvest Profit Pro - Farm Finance Management System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:seed": "sequelize-cli db:seed:all", "db:seed:undo": "sequelize-cli db:seed:undo:all", "db:create": "sequelize-cli db:create", "db:drop": "sequelize-cli db:drop"}, "keywords": ["farm", "finance", "agriculture", "management", "api"], "author": "Harvest Profit Pro Team", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "nodemailer": "^7.0.5", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"jest": "^30.0.5", "nodemon": "^3.1.10", "sequelize-cli": "^6.6.3", "supertest": "^7.1.4"}}