import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useExpenses } from '../../context/ExpenseContext';
import axios from 'axios';

const expenseCategories = [
  'seed',
  'fertilizer',
  'pesticide',
  'fuel',
  'equipment',
  'labor',
  'insurance',
  'utilities',
  'maintenance',
  'transportation',
  'storage',
  'marketing',
  'professional_services',
  'other'
];

const paymentMethods = [
  'cash',
  'check',
  'credit_card',
  'bank_transfer',
  'other'
];

// Validation schema
const schema = yup.object({
  description: yup
    .string()
    .required('Description is required')
    .max(500, 'Description must be less than 500 characters'),
  category: yup
    .string()
    .required('Category is required')
    .oneOf(expenseCategories, 'Invalid category'),
  amount: yup
    .number()
    .required('Amount is required')
    .positive('Amount must be positive'),
  date: yup
    .date()
    .required('Date is required')
    .max(new Date(), 'Date cannot be in the future'),
  quantity: yup
    .number()
    .nullable()
    .positive('Quantity must be positive'),
  vendor: yup
    .string()
    .max(100, 'Vendor name must be less than 100 characters'),
  invoiceNumber: yup
    .string()
    .max(50, 'Invoice number must be less than 50 characters'),
  notes: yup
    .string()
    .max(1000, 'Notes must be less than 1000 characters'),
});

function ExpenseModal({ open, onClose, onSuccess, expense }) {
  const { createExpense, updateExpense } = useExpenses();
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const isEditing = Boolean(expense);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      description: '',
      category: '',
      amount: '',
      date: new Date(),
      fieldId: '',
      quantity: '',
      unit: '',
      vendor: '',
      invoiceNumber: '',
      paymentMethod: 'cash',
      notes: '',
    },
  });

  useEffect(() => {
    if (open) {
      fetchFields();
      if (expense) {
        reset({
          description: expense.description || '',
          category: expense.category || '',
          amount: expense.amount || '',
          date: expense.date ? new Date(expense.date) : new Date(),
          fieldId: expense.fieldId || '',
          quantity: expense.quantity || '',
          unit: expense.unit || '',
          vendor: expense.vendor || '',
          invoiceNumber: expense.invoiceNumber || '',
          paymentMethod: expense.paymentMethod || 'cash',
          notes: expense.notes || '',
        });
      } else {
        reset({
          description: '',
          category: '',
          amount: '',
          date: new Date(),
          fieldId: '',
          quantity: '',
          unit: '',
          vendor: '',
          invoiceNumber: '',
          paymentMethod: 'cash',
          notes: '',
        });
      }
    }
  }, [open, expense, reset]);

  const fetchFields = async () => {
    try {
      const response = await axios.get('/fields');
      setFields(response.data.fields || []);
    } catch (error) {
      console.error('Failed to fetch fields:', error);
    }
  };

  const onSubmit = async (data) => {
    try {
      setError(null);
      setLoading(true);

      const expenseData = {
        ...data,
        date: data.date.toISOString().split('T')[0], // Format as YYYY-MM-DD
        fieldId: data.fieldId || null,
      };

      let result;
      if (isEditing) {
        result = await updateExpense(expense.id, expenseData);
      } else {
        result = await createExpense(expenseData);
      }

      if (result.success) {
        onSuccess();
      } else {
        setError(result.error);
      }
    } catch (error) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !loading) {
      setError(null);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle>
        <Typography variant="h6">
          {isEditing ? 'Edit Expense' : 'Add New Expense'}
        </Typography>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Description"
                    error={!!errors.description}
                    helperText={errors.description?.message}
                    multiline
                    rows={2}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="category"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.category}>
                    <InputLabel>Category</InputLabel>
                    <Select {...field} label="Category">
                      {expenseCategories.map((category) => (
                        <MenuItem key={category} value={category}>
                          {category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.category && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                        {errors.category.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="amount"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Amount"
                    type="number"
                    inputProps={{ step: '0.01', min: '0' }}
                    error={!!errors.amount}
                    helperText={errors.amount?.message}
                    InputProps={{
                      startAdornment: <Typography sx={{ mr: 1 }}>$</Typography>,
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="date"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    {...field}
                    label="Date"
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        error={!!errors.date}
                        helperText={errors.date?.message}
                      />
                    )}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="fieldId"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>Field (Optional)</InputLabel>
                    <Select {...field} label="Field (Optional)">
                      <MenuItem value="">None</MenuItem>
                      {fields.map((field) => (
                        <MenuItem key={field.id} value={field.id}>
                          {field.name} ({field.acres} acres)
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="quantity"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Quantity (Optional)"
                    type="number"
                    inputProps={{ step: '0.01', min: '0' }}
                    error={!!errors.quantity}
                    helperText={errors.quantity?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="unit"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Unit (Optional)"
                    placeholder="e.g., gallons, pounds, hours"
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="vendor"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Vendor (Optional)"
                    error={!!errors.vendor}
                    helperText={errors.vendor?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="invoiceNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Invoice Number (Optional)"
                    error={!!errors.invoiceNumber}
                    helperText={errors.invoiceNumber?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="paymentMethod"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>Payment Method</InputLabel>
                    <Select {...field} label="Payment Method">
                      {paymentMethods.map((method) => (
                        <MenuItem key={method} value={method}>
                          {method.charAt(0).toUpperCase() + method.slice(1).replace('_', ' ')}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="notes"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Notes (Optional)"
                    multiline
                    rows={3}
                    error={!!errors.notes}
                    helperText={errors.notes?.message}
                  />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 2 }}>
          <Button
            onClick={handleClose}
            disabled={isSubmitting || loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting || loading}
            startIcon={loading && <CircularProgress size={20} />}
          >
            {isEditing ? 'Update' : 'Create'} Expense
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}

export default ExpenseModal;
