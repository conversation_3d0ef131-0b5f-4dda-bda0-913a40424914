import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  useTheme,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Add as AddIcon, FilterList as FilterIcon } from '@mui/icons-material';
import { useExpenses } from '../../context/ExpenseContext';
import ExpenseModal from '../molecules/ExpenseModal';

const expenseCategories = [
  'seed',
  'fertilizer',
  'pesticide',
  'fuel',
  'equipment',
  'labor',
  'insurance',
  'utilities',
  'maintenance',
  'transportation',
  'storage',
  'marketing',
  'professional_services',
  'other'
];

function ExpenseTracking() {
  const theme = useTheme();
  const {
    expenses,
    isLoading,
    error,
    pagination,
    filters,
    fetchExpenses,
    setFilters,
    setPage,
    clearError
  } = useExpenses();

  const [modalOpen, setModalOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState(null);
  const [localFilters, setLocalFilters] = useState({
    category: '',
    startDate: null,
    endDate: null,
    search: ''
  });

  useEffect(() => {
    fetchExpenses();
  }, [fetchExpenses, filters, pagination.page]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  const handleFilterChange = (field, value) => {
    setLocalFilters(prev => ({ ...prev, [field]: value }));
  };

  const applyFilters = () => {
    const newFilters = {
      category: localFilters.category,
      startDate: localFilters.startDate ? localFilters.startDate.toISOString().split('T')[0] : '',
      endDate: localFilters.endDate ? localFilters.endDate.toISOString().split('T')[0] : '',
    };
    setFilters(newFilters);
  };

  const clearFilters = () => {
    setLocalFilters({
      category: '',
      startDate: null,
      endDate: null,
      search: ''
    });
    setFilters({
      category: '',
      startDate: '',
      endDate: '',
    });
  };

  const handleAddExpense = () => {
    setSelectedExpense(null);
    setModalOpen(true);
  };

  const handleEditExpense = (expense) => {
    setSelectedExpense(expense);
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
    setSelectedExpense(null);
  };

  const handleModalSuccess = () => {
    setModalOpen(false);
    setSelectedExpense(null);
    fetchExpenses(); // Refresh the list
  };

  const columns = [
    {
      field: 'date',
      headerName: 'Date',
      width: 120,
      valueFormatter: (params) => {
        return new Date(params.value).toLocaleDateString();
      }
    },
    {
      field: 'description',
      headerName: 'Description',
      width: 250,
      flex: 1
    },
    {
      field: 'category',
      headerName: 'Category',
      width: 150,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          color="primary"
          variant="outlined"
        />
      )
    },
    {
      field: 'amount',
      headerName: 'Amount',
      width: 120,
      type: 'number',
      valueFormatter: (params) => {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(params.value);
      }
    },
    {
      field: 'field',
      headerName: 'Field',
      width: 150,
      valueGetter: (params) => params.row.field?.name || 'N/A'
    },
    {
      field: 'vendor',
      headerName: 'Vendor',
      width: 150
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <Button
          size="small"
          onClick={() => handleEditExpense(params.row)}
        >
          Edit
        </Button>
      )
    }
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Expense Tracking
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Track and manage all your farm expenses in one place.
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          size="large"
          sx={{ px: 3 }}
          onClick={handleAddExpense}
        >
          Add Expense
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <FilterIcon sx={{ mr: 1 }} />
          <Typography variant="h6">
            Filters
          </Typography>
        </Box>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Category</InputLabel>
              <Select
                value={localFilters.category}
                label="Category"
                onChange={(e) => handleFilterChange('category', e.target.value)}
              >
                <MenuItem value="">All Categories</MenuItem>
                {expenseCategories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label="Start Date"
              value={localFilters.startDate}
              onChange={(date) => handleFilterChange('startDate', date)}
              renderInput={(params) => <TextField {...params} size="small" fullWidth />}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <DatePicker
              label="End Date"
              value={localFilters.endDate}
              onChange={(date) => handleFilterChange('endDate', date)}
              renderInput={(params) => <TextField {...params} size="small" fullWidth />}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                size="small"
                onClick={applyFilters}
              >
                Apply
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={clearFilters}
              >
                Clear
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Expense Table */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Expenses
        </Typography>
        <Box sx={{ height: 600, width: '100%' }}>
          <DataGrid
            rows={expenses}
            columns={columns}
            pageSize={pagination.limit}
            rowCount={pagination.total}
            page={pagination.page - 1}
            onPageChange={(newPage) => setPage(newPage + 1)}
            paginationMode="server"
            loading={isLoading}
            disableSelectionOnClick
            autoHeight
            sx={{
              '& .MuiDataGrid-cell:hover': {
                color: theme.palette.primary.main,
              },
            }}
          />
        </Box>
      </Paper>

      {/* Expense Modal */}
      <ExpenseModal
        open={modalOpen}
        onClose={handleModalClose}
        onSuccess={handleModalSuccess}
        expense={selectedExpense}
      />
    </Box>
  );
}

export default ExpenseTracking;
