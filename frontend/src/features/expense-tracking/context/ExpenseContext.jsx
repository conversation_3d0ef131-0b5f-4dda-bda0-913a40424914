import React, { createContext, useContext, useReducer } from 'react';
import axios from 'axios';

// Initial state
const initialState = {
  expenses: [],
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  },
  filters: {
    category: '',
    fieldId: '',
    startDate: '',
    endDate: '',
  },
};

// Action types
const EXPENSE_ACTIONS = {
  FETCH_EXPENSES_START: 'FETCH_EXPENSES_START',
  FETCH_EXPENSES_SUCCESS: 'FETCH_EXPENSES_SUCCESS',
  FETCH_EXPENSES_FAILURE: 'FETCH_EXPENSES_FAILURE',
  CREATE_EXPENSE_START: 'CREATE_EXPENSE_START',
  CREATE_EXPENSE_SUCCESS: 'CREATE_EXPENSE_SUCCESS',
  CREATE_EXPENSE_FAILURE: 'CREATE_EXPENSE_FAILURE',
  UPDATE_EXPENSE_SUCCESS: 'UPDATE_EXPENSE_SUCCESS',
  DELETE_EXPENSE_SUCCESS: 'DELETE_EXPENSE_SUCCESS',
  SET_FILTERS: 'SET_FILTERS',
  SET_PAGE: 'SET_PAGE',
  CLEAR_ERROR: 'CLEAR_ERROR',
};

// Reducer
function expenseReducer(state, action) {
  switch (action.type) {
    case EXPENSE_ACTIONS.FETCH_EXPENSES_START:
    case EXPENSE_ACTIONS.CREATE_EXPENSE_START:
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case EXPENSE_ACTIONS.FETCH_EXPENSES_SUCCESS:
      return {
        ...state,
        expenses: action.payload.expenses,
        pagination: action.payload.pagination,
        isLoading: false,
        error: null,
      };

    case EXPENSE_ACTIONS.CREATE_EXPENSE_SUCCESS:
      return {
        ...state,
        expenses: [action.payload.expense, ...state.expenses],
        isLoading: false,
        error: null,
      };

    case EXPENSE_ACTIONS.UPDATE_EXPENSE_SUCCESS:
      return {
        ...state,
        expenses: state.expenses.map(expense =>
          expense.id === action.payload.expense.id
            ? action.payload.expense
            : expense
        ),
        isLoading: false,
        error: null,
      };

    case EXPENSE_ACTIONS.DELETE_EXPENSE_SUCCESS:
      return {
        ...state,
        expenses: state.expenses.filter(expense => expense.id !== action.payload.expenseId),
        isLoading: false,
        error: null,
      };

    case EXPENSE_ACTIONS.FETCH_EXPENSES_FAILURE:
    case EXPENSE_ACTIONS.CREATE_EXPENSE_FAILURE:
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };

    case EXPENSE_ACTIONS.SET_FILTERS:
      return {
        ...state,
        filters: { ...state.filters, ...action.payload },
        pagination: { ...state.pagination, page: 1 }, // Reset to first page
      };

    case EXPENSE_ACTIONS.SET_PAGE:
      return {
        ...state,
        pagination: { ...state.pagination, page: action.payload },
      };

    case EXPENSE_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    default:
      return state;
  }
}

// Create context
const ExpenseContext = createContext();

// Expense provider component
export function ExpenseProvider({ children }) {
  const [state, dispatch] = useReducer(expenseReducer, initialState);

  // Fetch expenses
  const fetchExpenses = async () => {
    try {
      dispatch({ type: EXPENSE_ACTIONS.FETCH_EXPENSES_START });

      const params = new URLSearchParams({
        page: state.pagination.page,
        limit: state.pagination.limit,
        ...state.filters,
      });

      const response = await axios.get(`/expenses?${params}`);

      dispatch({
        type: EXPENSE_ACTIONS.FETCH_EXPENSES_SUCCESS,
        payload: response.data,
      });
    } catch (error) {
      dispatch({
        type: EXPENSE_ACTIONS.FETCH_EXPENSES_FAILURE,
        payload: error.response?.data?.error || 'Failed to fetch expenses',
      });
    }
  };

  // Create expense
  const createExpense = async (expenseData) => {
    try {
      dispatch({ type: EXPENSE_ACTIONS.CREATE_EXPENSE_START });

      const response = await axios.post('/expenses', expenseData);

      dispatch({
        type: EXPENSE_ACTIONS.CREATE_EXPENSE_SUCCESS,
        payload: response.data,
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to create expense';
      dispatch({
        type: EXPENSE_ACTIONS.CREATE_EXPENSE_FAILURE,
        payload: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  };

  // Update expense
  const updateExpense = async (expenseId, expenseData) => {
    try {
      const response = await axios.put(`/expenses/${expenseId}`, expenseData);

      dispatch({
        type: EXPENSE_ACTIONS.UPDATE_EXPENSE_SUCCESS,
        payload: response.data,
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to update expense';
      return { success: false, error: errorMessage };
    }
  };

  // Delete expense
  const deleteExpense = async (expenseId) => {
    try {
      await axios.delete(`/expenses/${expenseId}`);

      dispatch({
        type: EXPENSE_ACTIONS.DELETE_EXPENSE_SUCCESS,
        payload: { expenseId },
      });

      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.error || 'Failed to delete expense';
      return { success: false, error: errorMessage };
    }
  };

  // Set filters
  const setFilters = (filters) => {
    dispatch({
      type: EXPENSE_ACTIONS.SET_FILTERS,
      payload: filters,
    });
  };

  // Set page
  const setPage = (page) => {
    dispatch({
      type: EXPENSE_ACTIONS.SET_PAGE,
      payload: page,
    });
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: EXPENSE_ACTIONS.CLEAR_ERROR });
  };

  const value = {
    ...state,
    fetchExpenses,
    createExpense,
    updateExpense,
    deleteExpense,
    setFilters,
    setPage,
    clearError,
  };

  return (
    <ExpenseContext.Provider value={value}>
      {children}
    </ExpenseContext.Provider>
  );
}

// Custom hook to use expense context
export function useExpenses() {
  const context = useContext(ExpenseContext);
  if (!context) {
    throw new Error('useExpenses must be used within an ExpenseProvider');
  }
  return context;
}

export default ExpenseContext;
