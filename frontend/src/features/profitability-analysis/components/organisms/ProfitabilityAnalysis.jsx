import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  useTheme,
  Card,
  CardContent,
  Chip,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Button,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  Lightbulb as LightbulbIcon,
} from '@mui/icons-material';
import axios from 'axios';

function ProfitabilityAnalysis() {
  const theme = useTheme();
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [sortBy, setSortBy] = useState('profitPerAcre');
  const [sortOrder, setSortOrder] = useState('desc');

  useEffect(() => {
    fetchFieldData();
  }, []);

  const fetchFieldData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/fields');
      setFields(response.data.fields || []);
    } catch (error) {
      console.error('Failed to fetch field data:', error);
      setError('Failed to load field data');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  const sortedFields = [...fields].sort((a, b) => {
    const aValue = a[sortBy] || 0;
    const bValue = b[sortBy] || 0;

    if (sortOrder === 'asc') {
      return aValue - bValue;
    } else {
      return bValue - aValue;
    }
  });

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value || 0);
  };

  const getProfitabilityColor = (profitPerAcre) => {
    if (profitPerAcre > 100) return 'success';
    if (profitPerAcre > 0) return 'warning';
    return 'error';
  };

  const getProfitabilityIcon = (profitPerAcre) => {
    if (profitPerAcre > 0) {
      return <TrendingUpIcon color="success" />;
    } else {
      return <TrendingDownIcon color="error" />;
    }
  };

  const generateSuggestions = () => {
    const unprofitableFields = fields.filter(field => (field.profitPerAcre || 0) < 0);
    const lowProfitFields = fields.filter(field => (field.profitPerAcre || 0) > 0 && (field.profitPerAcre || 0) < 50);

    const suggestions = [];

    if (unprofitableFields.length > 0) {
      suggestions.push({
        type: 'critical',
        title: 'Address Unprofitable Fields',
        description: `${unprofitableFields.length} field(s) are operating at a loss. Consider reviewing input costs, crop selection, or soil management practices.`,
        fields: unprofitableFields.map(f => f.name).join(', ')
      });
    }

    if (lowProfitFields.length > 0) {
      suggestions.push({
        type: 'warning',
        title: 'Optimize Low-Profit Fields',
        description: `${lowProfitFields.length} field(s) have low profitability. Consider precision agriculture techniques or input optimization.`,
        fields: lowProfitFields.map(f => f.name).join(', ')
      });
    }

    const avgProfitPerAcre = fields.reduce((sum, field) => sum + (field.profitPerAcre || 0), 0) / fields.length;
    if (avgProfitPerAcre > 0) {
      suggestions.push({
        type: 'success',
        title: 'Overall Positive Performance',
        description: `Average profit per acre is ${formatCurrency(avgProfitPerAcre)}. Continue current practices and consider expanding successful strategies.`
      });
    }

    return suggestions;
  };

  const suggestions = generateSuggestions();

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={48} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mb: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        Profitability Analysis
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Analyze the profitability of your fields and identify areas for improvement.
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Total Fields
              </Typography>
              <Typography variant="h4" fontWeight="bold">
                {fields.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Total Acres
              </Typography>
              <Typography variant="h4" fontWeight="bold">
                {fields.reduce((sum, field) => sum + (field.acres || 0), 0).toFixed(1)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Avg Profit/Acre
              </Typography>
              <Typography variant="h4" fontWeight="bold" color={
                fields.length > 0 && fields.reduce((sum, field) => sum + (field.profitPerAcre || 0), 0) / fields.length > 0
                  ? 'success.main'
                  : 'error.main'
              }>
                {fields.length > 0
                  ? formatCurrency(fields.reduce((sum, field) => sum + (field.profitPerAcre || 0), 0) / fields.length)
                  : '$0.00'
                }
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Profitable Fields
              </Typography>
              <Typography variant="h4" fontWeight="bold" color="success.main">
                {fields.filter(field => (field.profitPerAcre || 0) > 0).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Field Performance Table */}
        <Grid item xs={12} lg={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Field Performance
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Field Name</TableCell>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortBy === 'acres'}
                        direction={sortBy === 'acres' ? sortOrder : 'asc'}
                        onClick={() => handleSort('acres')}
                      >
                        Acres
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortBy === 'revenue'}
                        direction={sortBy === 'revenue' ? sortOrder : 'asc'}
                        onClick={() => handleSort('revenue')}
                      >
                        Revenue
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortBy === 'totalExpenses'}
                        direction={sortBy === 'totalExpenses' ? sortOrder : 'asc'}
                        onClick={() => handleSort('totalExpenses')}
                      >
                        Expenses
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortBy === 'netProfit'}
                        direction={sortBy === 'netProfit' ? sortOrder : 'asc'}
                        onClick={() => handleSort('netProfit')}
                      >
                        Net Profit
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="right">
                      <TableSortLabel
                        active={sortBy === 'profitPerAcre'}
                        direction={sortBy === 'profitPerAcre' ? sortOrder : 'asc'}
                        onClick={() => handleSort('profitPerAcre')}
                      >
                        Profit/Acre
                      </TableSortLabel>
                    </TableCell>
                    <TableCell align="center">Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {sortedFields.map((field) => (
                    <TableRow
                      key={field.id}
                      sx={{
                        backgroundColor: (field.profitPerAcre || 0) < 0
                          ? theme.palette.error.light + '20'
                          : 'inherit'
                      }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {getProfitabilityIcon(field.profitPerAcre)}
                          <Box sx={{ ml: 1 }}>
                            <Typography variant="body2" fontWeight="bold">
                              {field.name}
                            </Typography>
                            {field.currentCrop && (
                              <Typography variant="caption" color="text.secondary">
                                {field.currentCrop}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell align="right">{field.acres?.toFixed(1) || '0.0'}</TableCell>
                      <TableCell align="right">{formatCurrency(field.revenue)}</TableCell>
                      <TableCell align="right">{formatCurrency(field.totalExpenses)}</TableCell>
                      <TableCell align="right" sx={{
                        color: (field.netProfit || 0) >= 0 ? 'success.main' : 'error.main',
                        fontWeight: 'bold'
                      }}>
                        {formatCurrency(field.netProfit)}
                      </TableCell>
                      <TableCell align="right" sx={{
                        color: (field.profitPerAcre || 0) >= 0 ? 'success.main' : 'error.main',
                        fontWeight: 'bold'
                      }}>
                        {formatCurrency(field.profitPerAcre)}
                      </TableCell>
                      <TableCell align="center">
                        <Chip
                          label={(field.profitPerAcre || 0) >= 0 ? 'Profitable' : 'Loss'}
                          color={getProfitabilityColor(field.profitPerAcre)}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            {fields.length === 0 && (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  No field data available. Add fields to see profitability analysis.
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Improvement Suggestions */}
        <Grid item xs={12} lg={4}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LightbulbIcon sx={{ mr: 1, color: 'warning.main' }} />
              <Typography variant="h6">
                Improvement Suggestions
              </Typography>
            </Box>

            {suggestions.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {suggestions.map((suggestion, index) => (
                  <Card
                    key={index}
                    variant="outlined"
                    sx={{
                      borderColor: suggestion.type === 'critical' ? 'error.main' :
                                  suggestion.type === 'warning' ? 'warning.main' : 'success.main'
                    }}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        {suggestion.type === 'critical' && <WarningIcon color="error" sx={{ mr: 1 }} />}
                        {suggestion.type === 'warning' && <WarningIcon color="warning" sx={{ mr: 1 }} />}
                        {suggestion.type === 'success' && <TrendingUpIcon color="success" sx={{ mr: 1 }} />}
                        <Typography variant="subtitle2" fontWeight="bold">
                          {suggestion.title}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {suggestion.description}
                      </Typography>
                      {suggestion.fields && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Fields: {suggestion.fields}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </Box>
            ) : (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  No suggestions available. Add field data to get recommendations.
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default ProfitabilityAnalysis;
