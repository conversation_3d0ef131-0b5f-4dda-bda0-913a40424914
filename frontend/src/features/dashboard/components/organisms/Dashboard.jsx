import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  useTheme,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  Agriculture as AgricultureIcon,
} from '@mui/icons-material';
import axios from 'axios';

// Helper function to get KPI icons
const getKPIIcon = (title) => {
  switch (title) {
    case 'Total Revenue':
      return <MoneyIcon />;
    case 'Total Expenses':
      return <TrendingDownIcon />;
    case 'Net Profit':
      return <TrendingUpIcon />;
    case 'Profit Margin':
      return <AgricultureIcon />;
    default:
      return <MoneyIcon />;
  }
};

// KPI Card Component
function KPICard({ title, value, change, icon, format = 'currency' }) {
  const theme = useTheme();
  const isPositive = change >= 0;
  
  const formatValue = (val) => {
    if (format === 'currency') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(val);
    }
    if (format === 'percentage') {
      return `${val.toFixed(1)}%`;
    }
    return val.toLocaleString();
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box
            sx={{
              p: 1,
              borderRadius: 1,
              backgroundColor: theme.palette.primary.light + '20',
              color: theme.palette.primary.main,
              mr: 2,
            }}
          >
            {icon}
          </Box>
          <Typography variant="h6" component="h3" color="text.secondary">
            {title}
          </Typography>
        </Box>
        
        <Typography variant="h4" component="p" fontWeight="bold" gutterBottom>
          {formatValue(value)}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {isPositive ? (
            <TrendingUpIcon sx={{ color: 'success.main', mr: 0.5 }} />
          ) : (
            <TrendingDownIcon sx={{ color: 'error.main', mr: 0.5 }} />
          )}
          <Typography
            variant="body2"
            sx={{
              color: isPositive ? 'success.main' : 'error.main',
              fontWeight: 600,
            }}
          >
            {Math.abs(change).toFixed(1)}% from last month
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
}

function Dashboard() {
  const theme = useTheme();
  const [dashboardData, setDashboardData] = useState(null);
  const [kpis, setKpis] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
    fetchKPIs();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/dashboard');
      setDashboardData(response.data);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      setError('Failed to load dashboard data');
    }
  };

  const fetchKPIs = async () => {
    try {
      const response = await axios.get('/dashboard/kpis');
      setKpis(response.data.kpis);
    } catch (error) {
      console.error('Failed to fetch KPIs:', error);
      setError('Failed to load KPI data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={48} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mb: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        Dashboard
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        Welcome back! Here's an overview of your farm's financial performance.
      </Typography>

      {/* KPI Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {kpis.map((kpi, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <KPICard
              {...kpi}
              icon={getKPIIcon(kpi.title)}
            />
          </Grid>
        ))}
      </Grid>

      {/* Charts and Additional Content */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Revenue vs Expenses Trend
            </Typography>
            {dashboardData?.charts?.profitabilityTrend ? (
              <Box sx={{ height: '80%' }}>
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 10 }}>
                  Chart.js integration will display profitability trends here
                </Typography>
              </Box>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '80%',
                  backgroundColor: theme.palette.grey[50],
                  borderRadius: 1,
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  No data available
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Field Performance Summary
            </Typography>
            {dashboardData?.fields?.topPerforming?.length > 0 ? (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Top Performing Fields:
                </Typography>
                {dashboardData.fields.topPerforming.slice(0, 3).map((field, index) => (
                  <Box key={field.id} sx={{ mb: 2, p: 2, backgroundColor: theme.palette.grey[50], borderRadius: 1 }}>
                    <Typography variant="body2" fontWeight="bold">
                      {field.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {field.acres} acres • ${field.profitPerAcre?.toFixed(2)}/acre profit
                    </Typography>
                  </Box>
                ))}
                {dashboardData.fields.needingAttention?.length > 0 && (
                  <>
                    <Typography variant="subtitle2" gutterBottom sx={{ mt: 2, color: 'error.main' }}>
                      Fields Needing Attention:
                    </Typography>
                    {dashboardData.fields.needingAttention.slice(0, 2).map((field) => (
                      <Box key={field.id} sx={{ mb: 1, p: 2, backgroundColor: theme.palette.error.light + '20', borderRadius: 1 }}>
                        <Typography variant="body2" fontWeight="bold">
                          {field.name}
                        </Typography>
                        <Typography variant="caption" color="error.main">
                          ${field.profitPerAcre?.toFixed(2)}/acre loss
                        </Typography>
                      </Box>
                    ))}
                  </>
                )}
              </Box>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '80%',
                  backgroundColor: theme.palette.grey[50],
                  borderRadius: 1,
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  No field data available
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Activity */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activity
            </Typography>
            {dashboardData?.recentActivity?.expenses?.length > 0 ? (
              <Box>
                {dashboardData.recentActivity.expenses.slice(0, 5).map((expense) => (
                  <Box key={expense.id} sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    py: 1,
                    borderBottom: `1px solid ${theme.palette.divider}`
                  }}>
                    <Box>
                      <Typography variant="body2" fontWeight="bold">
                        {expense.description}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {expense.category} • {new Date(expense.date).toLocaleDateString()}
                        {expense.field && ` • ${expense.field.name}`}
                      </Typography>
                    </Box>
                    <Typography variant="body2" fontWeight="bold" color="error.main">
                      ${expense.amount?.toLocaleString()}
                    </Typography>
                  </Box>
                ))}
              </Box>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 200,
                  backgroundColor: theme.palette.grey[50],
                  borderRadius: 1,
                }}
              >
                <Typography variant="body1" color="text.secondary">
                  No recent activity
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
