import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  useTheme,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  Agriculture as AgricultureIcon,
  WbSunny as WeatherIcon,
  Opacity as WaterIcon,
  Eco as EcoIcon,
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import axios from 'axios';

// Helper function to get KPI icons
const getKPIIcon = (title) => {
  switch (title) {
    case 'Total Revenue':
      return <MoneyIcon />;
    case 'Total Expenses':
      return <TrendingDownIcon />;
    case 'Net Profit':
      return <TrendingUpIcon />;
    case 'Profit Margin':
      return <AgricultureIcon />;
    default:
      return <MoneyIcon />;
  }
};

// Modern KPI Card Component - Matching Visual Identity
function ModernKPICard({ title, value, change, icon, format = 'currency', color = 'primary' }) {
  const theme = useTheme();
  const isPositive = change >= 0;

  const formatValue = (val) => {
    if (format === 'currency') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(val);
    }
    if (format === 'percentage') {
      return `${val.toFixed(1)}%`;
    }
    return val.toLocaleString();
  };

  const getColorScheme = (colorType) => {
    switch (colorType) {
      case 'success':
        return {
          bg: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
          light: 'rgba(16, 185, 129, 0.1)',
        };
      case 'warning':
        return {
          bg: 'linear-gradient(135deg, #F59E0B 0%, #D97706 100%)',
          light: 'rgba(245, 158, 11, 0.1)',
        };
      case 'error':
        return {
          bg: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
          light: 'rgba(239, 68, 68, 0.1)',
        };
      default:
        return {
          bg: 'linear-gradient(135deg, #84CC16 0%, #65A30D 100%)',
          light: 'rgba(132, 204, 22, 0.1)',
        };
    }
  };

  const colorScheme = getColorScheme(color);

  return (
    <Card
      sx={{
        height: '100%',
        background: '#FFFFFF',
        border: '1px solid rgba(0,0,0,0.05)',
        borderRadius: 3,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
        },
      }}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box>
            <Typography
              variant="body2"
              sx={{
                color: '#6B7280',
                fontWeight: 500,
                fontSize: '0.75rem',
                textTransform: 'uppercase',
                letterSpacing: '0.05em',
                mb: 1,
              }}
            >
              {title}
            </Typography>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 700,
                color: '#1F2937',
                fontSize: '2rem',
                lineHeight: 1,
              }}
            >
              {formatValue(value)}
            </Typography>
          </Box>

          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: 2,
              background: colorScheme.bg,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
            }}
          >
            {icon}
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
          <Chip
            size="small"
            label={`${isPositive ? '+' : ''}${change.toFixed(1)}%`}
            sx={{
              backgroundColor: isPositive ? 'rgba(16, 185, 129, 0.1)' : 'rgba(239, 68, 68, 0.1)',
              color: isPositive ? '#059669' : '#DC2626',
              fontWeight: 600,
              fontSize: '0.75rem',
              height: 24,
              '& .MuiChip-label': {
                px: 1,
              },
            }}
          />
          <Typography
            variant="caption"
            sx={{
              ml: 1,
              color: '#6B7280',
              fontSize: '0.75rem',
            }}
          >
            vs last month
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
}

// Weather Widget Component
function WeatherWidget() {
  const theme = useTheme();

  return (
    <Card sx={{ height: '100%', background: 'linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)', color: 'white' }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="body2" sx={{ opacity: 0.8, mb: 1 }}>
              Weather
            </Typography>
            <Typography variant="h2" sx={{ fontWeight: 700, mb: 0.5 }}>
              30°
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              Cloudy
            </Typography>
          </Box>
          <WeatherIcon sx={{ fontSize: 40, opacity: 0.8 }} />
        </Box>

        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>Wind</Typography>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>12 mph</Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="caption" sx={{ opacity: 0.8 }}>Humidity</Typography>
            <Typography variant="body2" sx={{ fontWeight: 600 }}>68%</Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}

// Schedule Card Component
function ScheduleCard() {
  const scheduleItems = [
    { task: 'Pruning Peaches', time: '8:00 AM', status: 'pending' },
    { task: 'Pruning Apples', time: '2:00 PM', status: 'pending' },
  ];

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <ScheduleIcon sx={{ mr: 1, color: '#6B7280' }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Schedule For The Day
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Typography variant="h1" sx={{ fontWeight: 700, color: '#1F2937', mr: 1 }}>
            28
          </Typography>
          <Typography variant="body2" sx={{ color: '#6B7280' }}>
            December
          </Typography>
        </Box>

        {scheduleItems.map((item, index) => (
          <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: '#84CC16',
                mr: 2,
              }}
            />
            <Box sx={{ flex: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {item.task}
              </Typography>
              <Typography variant="caption" sx={{ color: '#6B7280' }}>
                {item.time}
              </Typography>
            </Box>
            <Chip
              label="Pending"
              size="small"
              sx={{
                backgroundColor: '#FEF3C7',
                color: '#92400E',
                fontSize: '0.7rem',
              }}
            />
          </Box>
        ))}
      </CardContent>
    </Card>
  );
}

// Crop Status Cards
function CropStatusCard({ title, image, status, color }) {
  return (
    <Card sx={{ height: 120, position: 'relative', overflow: 'hidden' }}>
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, ${color} 0%, ${color}CC 100%)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          color: 'white',
        }}
      >
        <Box>
          <Typography variant="body2" sx={{ opacity: 0.9, mb: 0.5 }}>
            {title}
          </Typography>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {status}
          </Typography>
        </Box>
        <Box
          sx={{
            width: 60,
            height: 60,
            borderRadius: 2,
            backgroundColor: 'rgba(255,255,255,0.2)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <EcoIcon sx={{ fontSize: 30 }} />
        </Box>
      </Box>
    </Card>
  );
}

function Dashboard() {
  const theme = useTheme();
  const [dashboardData, setDashboardData] = useState(null);
  const [kpis, setKpis] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
    fetchKPIs();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/dashboard');
      setDashboardData(response.data);
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      setError('Failed to load dashboard data');
    }
  };

  const fetchKPIs = async () => {
    try {
      const response = await axios.get('/dashboard/kpis');
      setKpis(response.data.kpis);
    } catch (error) {
      console.error('Failed to fetch KPIs:', error);
      setError('Failed to load KPI data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={48} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mb: 4 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Mock data for demo
  const mockKpis = [
    { title: 'Total Revenue', value: 125000, change: 12.5, format: 'currency', color: 'success' },
    { title: 'Total Expenses', value: 85000, change: -5.2, format: 'currency', color: 'warning' },
    { title: 'Net Profit', value: 40000, change: 8.7, format: 'currency', color: 'primary' },
    { title: 'Profit Margin', value: 32, change: 3.1, format: 'percentage', color: 'primary' },
  ];

  return (
    <Box sx={{ p: 3, backgroundColor: '#F8F9FA', minHeight: '100vh' }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h3"
          sx={{
            fontWeight: 700,
            color: '#1F2937',
            mb: 1,
            fontSize: '2.5rem',
          }}
        >
          My Farm
        </Typography>
        <Typography variant="body1" sx={{ color: '#6B7280', fontSize: '1rem' }}>
          Welcome back! Here's your farm overview for today.
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Left Column */}
        <Grid item xs={12} lg={8}>
          {/* KPI Cards Row */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            {mockKpis.map((kpi, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <ModernKPICard
                  {...kpi}
                  icon={getKPIIcon(kpi.title)}
                />
              </Grid>
            ))}
          </Grid>

          {/* Schedule and Farm Map Row */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} md={6}>
              <ScheduleCard />
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: 300, position: 'relative', overflow: 'hidden' }}>
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'linear-gradient(135deg, #84CC16 0%, #65A30D 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                  }}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <AgricultureIcon sx={{ fontSize: 60, mb: 2, opacity: 0.8 }} />
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      Farm Map
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.9 }}>
                      Interactive field view coming soon
                    </Typography>
                  </Box>
                </Box>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} lg={4}>
          <Grid container spacing={3}>
            {/* Weather Widget */}
            <Grid item xs={12}>
              <WeatherWidget />
            </Grid>

            {/* Crop Status Cards */}
            <Grid item xs={6}>
              <CropStatusCard
                title="Apples"
                status="Good"
                color="#10B981"
              />
            </Grid>
            <Grid item xs={6}>
              <CropStatusCard
                title="Cherries"
                status="Alert"
                color="#EF4444"
              />
            </Grid>

            {/* Farm Stats */}
            <Grid item xs={12}>
              <Card sx={{ height: 200 }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                    Farm Stats
                  </Typography>

                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" sx={{ color: '#6B7280' }}>
                        Water Usage
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        26.7%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={26.7}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: '#E5E7EB',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: '#3B82F6',
                          borderRadius: 3,
                        },
                      }}
                    />
                  </Box>

                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" sx={{ color: '#6B7280' }}>
                        Crop Health
                      </Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        85%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={85}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: '#E5E7EB',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: '#84CC16',
                          borderRadius: 3,
                        },
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );

export default Dashboard;
