import React from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { Agriculture as AgricultureIcon } from '@mui/icons-material';

function AuthLayout({ children }) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: theme.spacing(2),
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={24}
          sx={{
            padding: theme.spacing(4),
            borderRadius: theme.spacing(2),
            textAlign: 'center',
          }}
        >
          {/* Logo and Brand */}
          <Box sx={{ mb: 4 }}>
            <AgricultureIcon
              sx={{
                fontSize: 48,
                color: theme.palette.primary.main,
                mb: 2,
              }}
            />
            <Typography
              variant="h4"
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 700,
                color: theme.palette.primary.main,
                fontSize: isMobile ? '1.75rem' : '2.125rem',
              }}
            >
              Harvest Profit Pro
            </Typography>
            <Typography
              variant="subtitle1"
              color="text.secondary"
              sx={{
                mb: 3,
                fontSize: isMobile ? '0.875rem' : '1rem',
              }}
            >
              Farm Finance Management System
            </Typography>
          </Box>

          {/* Auth Form Content */}
          {children}

          {/* Footer */}
          <Box sx={{ mt: 4, pt: 3, borderTop: `1px solid ${theme.palette.divider}` }}>
            <Typography variant="body2" color="text.secondary">
              © 2024 Harvest Profit Pro. All rights reserved.
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
}

export default AuthLayout;
